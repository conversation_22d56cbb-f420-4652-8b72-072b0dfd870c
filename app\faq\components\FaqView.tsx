'use client';

import React, { useState, useEffect, useCallback } from 'react';
import Modal from '../../components/ui/Modal';
import { Faq, formatDate, getServiceOption, getPageTypeOption } from '../faq.model';
import FaqService from '../faq.service';

interface FaqViewProps {
  faq: Faq | null;
  isOpen: boolean;
  onClose: () => void;
  onEdit?: (faq: Faq) => void;
  onViewRelated?: (faq: Faq) => void;
}

export default function FaqView({ faq, isOpen, onClose, onEdit, onViewRelated }: FaqViewProps) {
  const [relatedFaqs, setRelatedFaqs] = useState<Faq[]>([]);
  const [loadingRelated, setLoadingRelated] = useState(false);

  const loadRelatedFaqs = useCallback(async () => {
    if (!faq) return;

    try {
      setLoadingRelated(true);
      const faqs = await FaqService.getFaqsByLanguage(faq.language_code);
      // Filter out the current FAQ and limit to 5 related FAQs
      const filtered = faqs.filter(f => f.ID !== faq.ID).slice(0, 5);
      setRelatedFaqs(filtered);
    } catch (error) {
      console.error('Error loading related FAQs:', error);
      setRelatedFaqs([]);
    } finally {
      setLoadingRelated(false);
    }
  }, [faq]);

  // Load related FAQs in the same language
  useEffect(() => {
    if (faq && isOpen) {
      loadRelatedFaqs();
    }
  }, [faq, isOpen, loadRelatedFaqs]);

  if (!faq) return null;

  const serviceOption = getServiceOption(faq.service);
  const pageTypeOption = getPageTypeOption(faq.page_type);

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={`FAQ #${faq.ID}`}
      subtitle={`${faq.language} • ${serviceOption?.label || faq.service} • ${pageTypeOption?.label || faq.page_type}`}
      size="lg"
      height="fixed"
      headerActions={
        onEdit && (
          <button
            onClick={() => onEdit(faq)}
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors shadow-sm"
          >
            <i className="ri-edit-line mr-2 text-sm"></i>
            Edit FAQ
          </button>
        )
      }
    >
      <div className="space-y-6">
        {/* FAQ Details */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="text-sm font-medium text-gray-900 mb-3">Basic Information</h4>
              <div className="space-y-3">
                <div>
                  <label className="text-xs font-medium text-gray-500 uppercase tracking-wider">FAQ ID</label>
                  <p className="text-sm text-gray-900 mt-1">#{faq.ID}</p>
                </div>
                <div>
                  <label className="text-xs font-medium text-gray-500 uppercase tracking-wider">Service ID</label>
                  <p className="text-sm text-gray-900 mt-1">{faq.service_id}</p>
                </div>
                <div>
                  <label className="text-xs font-medium text-gray-500 uppercase tracking-wider">Service</label>
                  <div className="mt-1">
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                      {serviceOption?.label || faq.service}
                    </span>
                  </div>
                </div>
                <div>
                  <label className="text-xs font-medium text-gray-500 uppercase tracking-wider">Page Type</label>
                  <div className="mt-1">
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                      {pageTypeOption?.label || faq.page_type}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Language & Timestamps */}
          <div className="space-y-4">
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="text-sm font-medium text-gray-900 mb-3">Language & Timestamps</h4>
              <div className="space-y-3">
                <div>
                  <label className="text-xs font-medium text-gray-500 uppercase tracking-wider">Language</label>
                  <div className="flex items-center mt-1">
                    <div className="flex-shrink-0 h-8 w-8">
                      <div className="h-8 w-8 rounded-lg bg-gradient-to-br from-purple-100 to-purple-200 flex items-center justify-center">
                        <span className="text-purple-700 text-xs font-medium">
                          {faq.language_code.toUpperCase()}
                        </span>
                      </div>
                    </div>
                    <div className="ml-3">
                      <div className="text-sm font-medium text-gray-900">{faq.language}</div>
                      <div className="text-xs text-gray-500">{faq.language_code}</div>
                    </div>
                  </div>
                </div>
                <div>
                  <label className="text-xs font-medium text-gray-500 uppercase tracking-wider">Created</label>
                  <p className="text-sm text-gray-900 mt-1">{formatDate(faq.CreatedAt)}</p>
                </div>
                <div>
                  <label className="text-xs font-medium text-gray-500 uppercase tracking-wider">Last Updated</label>
                  <p className="text-sm text-gray-900 mt-1">{formatDate(faq.UpdatedAt)}</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Question */}
        <div className="bg-blue-50 rounded-lg p-4">
          <h4 className="text-sm font-medium text-blue-900 mb-3 flex items-center">
            <i className="ri-question-line mr-2"></i>
            Question
          </h4>
          <div className="text-sm text-blue-800 leading-relaxed">
            {faq.question}
          </div>
        </div>

        {/* Answer */}
        <div className="bg-green-50 rounded-lg p-4">
          <h4 className="text-sm font-medium text-green-900 mb-3 flex items-center">
            <i className="ri-chat-check-line mr-2"></i>
            Answer
          </h4>
          <div className="text-sm text-green-800 leading-relaxed whitespace-pre-wrap">
            {faq.answer}
          </div>
        </div>

        {/* Metadata */}
        <div className="border-t border-gray-200 pt-4">
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 text-center">
            <div className="bg-gray-50 rounded-lg p-3">
              <div className="text-lg font-semibold text-gray-900">#{faq.ID}</div>
              <div className="text-xs text-gray-500">FAQ ID</div>
            </div>
            <div className="bg-gray-50 rounded-lg p-3">
              <div className="text-lg font-semibold text-gray-900">{faq.service_id}</div>
              <div className="text-xs text-gray-500">Service ID</div>
            </div>
            <div className="bg-gray-50 rounded-lg p-3">
              <div className="text-lg font-semibold text-gray-900">{faq.language_code.toUpperCase()}</div>
              <div className="text-xs text-gray-500">Language Code</div>
            </div>
            <div className="bg-gray-50 rounded-lg p-3">
              <div className="text-lg font-semibold text-gray-900">
                {faq.question.length + faq.answer.length}
              </div>
              <div className="text-xs text-gray-500">Total Characters</div>
            </div>
          </div>
        </div>

        {/* Related FAQs in Same Language */}
        {relatedFaqs.length > 0 && (
          <div className="border-t border-gray-200 pt-6">
            <div className="flex items-center justify-between mb-4">
              <h4 className="text-lg font-medium text-gray-900 flex items-center">
                <i className="ri-links-line mr-2 text-purple-600"></i>
                Related FAQs in {faq.language}
              </h4>
              <span className="text-sm text-gray-500">
                {relatedFaqs.length} of {relatedFaqs.length + 1} total
              </span>
            </div>

            {loadingRelated ? (
              <div className="flex items-center justify-center py-8">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-purple-600"></div>
                <span className="ml-2 text-sm text-gray-600">Loading related FAQs...</span>
              </div>
            ) : (
              <div className="space-y-3">
                {relatedFaqs.map((relatedFaq) => (
                  <div
                    key={relatedFaq.ID}
                    className="bg-purple-50 border border-purple-200 rounded-lg p-4 hover:bg-purple-100 transition-colors cursor-pointer"
                    onClick={() => onViewRelated && onViewRelated(relatedFaq)}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-2">
                          <span className="text-xs font-medium text-purple-600">#{relatedFaq.ID}</span>
                          <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                            {getServiceOption(relatedFaq.service)?.label || relatedFaq.service}
                          </span>
                          <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                            {getPageTypeOption(relatedFaq.page_type)?.label || relatedFaq.page_type}
                          </span>
                        </div>
                        <h5 className="text-sm font-medium text-gray-900 mb-1">
                          {relatedFaq.question.length > 80
                            ? relatedFaq.question.substring(0, 80) + '...'
                            : relatedFaq.question
                          }
                        </h5>
                        <p className="text-xs text-gray-600">
                          {relatedFaq.answer.length > 100
                            ? relatedFaq.answer.substring(0, 100) + '...'
                            : relatedFaq.answer
                          }
                        </p>
                      </div>
                      <button className="ml-3 text-purple-600 hover:text-purple-800">
                        <i className="ri-arrow-right-line"></i>
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex items-center justify-end space-x-3 pt-4 border-t border-gray-200">
          <button
            onClick={onClose}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:ring-2 focus:ring-blue-500"
          >
            Close
          </button>
          {onEdit && (
            <button
              onClick={() => onEdit(faq)}
              className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500"
            >
              <i className="ri-edit-line mr-2"></i>
              Edit FAQ
            </button>
          )}
        </div>
      </div>
    </Modal>
  );
}
