'use client';

import { useState, useEffect, useCallback } from 'react';
import { Offer, OfferFilters, OfferStats, getDefaultOfferFilters } from '../offer.model';
import OfferService from '../offer.service';
import OfferList from './OfferList';
import OfferAddEdit from './OfferAddEdit';
import OfferView from './OfferView';
import OfferFiltersComponent from './OfferFilters';
import Modal from '../../components/ui/Modal';
import PageSectionHeader from '../../components/ui/PageSectionHeader';

export default function OfferMaster() {
  // State management
  const [offers, setOffers] = useState<Offer[]>([]);
  const [filteredOffers, setFilteredOffers] = useState<Offer[]>([]);
  const [stats, setStats] = useState<OfferStats>({
    totalOffers: 0,
    activeOffers: 0,
    expiredOffers: 0,
    draftOffers: 0,
    lastUpdated: new Date().toISOString()
  });
  const [filters, setFilters] = useState<OfferFilters>(getDefaultOfferFilters());
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [totalItems, setTotalItems] = useState(0);

  // Modal states
  const [isAddEditOpen, setIsAddEditOpen] = useState(false);
  const [isViewOpen, setIsViewOpen] = useState(false);
  const [selectedOffer, setSelectedOffer] = useState<Offer | null>(null);
  const [editMode, setEditMode] = useState<'add' | 'edit'>('add');

  // Fetch offers with filters - API returns simple array, implement client-side pagination
  const fetchOffers = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const allOffers = await OfferService.getAllOffers(filters);
      setOffers(allOffers);

      // Calculate client-side pagination
      const totalItems = allOffers.length;
      const totalPages = Math.ceil(totalItems / pageSize);

      // Apply pagination to filtered offers
      const startIndex = (currentPage - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      const paginatedOffers = allOffers.slice(startIndex, endIndex);

      setFilteredOffers(paginatedOffers);
      setTotalPages(totalPages);
      setTotalItems(totalItems);

      // Reset to page 1 if current page is beyond available pages
      if (currentPage > totalPages && totalPages > 0) {
        setCurrentPage(1);
      }
    } catch (err) {
      console.error('Error fetching offers:', err);
      setError('Failed to load offers. Please try again.');
      setOffers([]);
      setFilteredOffers([]);
    } finally {
      setLoading(false);
    }
  }, [filters, currentPage, pageSize]);

  // Calculate stats from current offers
  const calculateStats = useCallback(() => {
    const now = new Date();
    const activeOffers = offers.filter(offer => {
      if (offer.deleted_at) return false;
      const validFrom = offer.valid_from ? new Date(offer.valid_from) : null;
      const validTo = offer.valid_to ? new Date(offer.valid_to) : null;

      if (validFrom && validFrom > now) return false;
      if (validTo && validTo < now) return false;
      return true;
    }).length;

    const expiredOffers = offers.filter(offer => {
      if (offer.deleted_at) return false;
      const validTo = offer.valid_to ? new Date(offer.valid_to) : null;
      return validTo && validTo < now;
    }).length;

    const draftOffers = offers.filter(offer => {
      if (offer.deleted_at) return false;
      const validFrom = offer.valid_from ? new Date(offer.valid_from) : null;
      return validFrom && validFrom > now;
    }).length;

    const totalOffers = offers.filter(offer => !offer.deleted_at).length;

    setStats({
      totalOffers,
      activeOffers,
      expiredOffers,
      draftOffers,
      lastUpdated: new Date().toISOString()
    });
  }, [offers]);

  useEffect(() => {
    fetchOffers();
  }, [fetchOffers]);

  useEffect(() => {
    calculateStats();
  }, [calculateStats]);

  // Handle pagination changes when offers data is already loaded
  useEffect(() => {
    if (offers.length > 0) {
      const startIndex = (currentPage - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      const paginatedOffers = offers.slice(startIndex, endIndex);
      setFilteredOffers(paginatedOffers);
    }
  }, [currentPage, pageSize, offers]);

  // Event handlers
  const handleCreateOffer = () => {
    setSelectedOffer(null);
    setEditMode('add');
    setIsAddEditOpen(true);
  };

  const handleEditOffer = (offer: Offer) => {
    setSelectedOffer(offer);
    setEditMode('edit');
    setIsAddEditOpen(true);
  };

  const handleViewOffer = (offer: Offer) => {
    setSelectedOffer(offer);
    setIsViewOpen(true);
  };

  const handleDeleteOffer = async (offer: Offer) => {
    if (window.confirm(`Are you sure you want to delete the offer "${offer.name}"?`)) {
      try {
        await OfferService.deleteOffer(offer.id);
        await fetchOffers();
      } catch (error) {
        console.error('Error deleting offer:', error);
        alert('Failed to delete offer. Please try again.');
      }
    }
  };

  const handleSaveOffer = async () => {
    setIsAddEditOpen(false);
    setSelectedOffer(null);
    await fetchOffers();
  };

  const handleFiltersChange = (newFilters: OfferFilters) => {
    setFilters(newFilters);
    setCurrentPage(1); // Reset to first page when filters change
  };

  const handleClearFilters = () => {
    setFilters(getDefaultOfferFilters());
    setCurrentPage(1);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handlePageSizeChange = (newPageSize: number) => {
    setPageSize(newPageSize);
    setCurrentPage(1);
  };

  const handleViewToEdit = (offer: Offer) => {
    setIsViewOpen(false);
    setSelectedOffer(offer);
    setEditMode('edit');
    setIsAddEditOpen(true);
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <PageSectionHeader
        title="Offer Management"
        subtitle="Create and manage special offers for your travel services"
        totalItems={totalItems}
        showAddButton={true}
        addButtonText="Add Offer"
        onAddButtonClick={handleCreateOffer}
      />

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                <i className="ri-gift-line text-blue-600"></i>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Total Offers</p>
              <p className="text-2xl font-semibold text-gray-900">{stats.totalOffers}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                <i className="ri-check-line text-green-600"></i>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Active Offers</p>
              <p className="text-2xl font-semibold text-gray-900">{stats.activeOffers}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center">
                <i className="ri-time-line text-red-600"></i>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Expired Offers</p>
              <p className="text-2xl font-semibold text-gray-900">{stats.expiredOffers}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
                <i className="ri-draft-line text-yellow-600"></i>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Draft Offers</p>
              <p className="text-2xl font-semibold text-gray-900">{stats.draftOffers}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <OfferFiltersComponent
        filters={filters}
        onFiltersChange={handleFiltersChange}
        onClearFilters={handleClearFilters}
      />

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex">
            <i className="ri-error-warning-line text-red-400 mr-2"></i>
            <p className="text-sm text-red-700">{error}</p>
          </div>
        </div>
      )}

      {/* Offer List */}
      <OfferList
        offers={filteredOffers}
        onEdit={handleEditOffer}
        onView={handleViewOffer}
        onDelete={handleDeleteOffer}
        loading={loading}
        currentPage={currentPage}
        totalPages={totalPages}
        pageSize={pageSize}
        totalItems={totalItems}
        onPageChange={handlePageChange}
        onPageSizeChange={handlePageSizeChange}
      />

      {/* Add/Edit Modal */}
      <Modal
        isOpen={isAddEditOpen}
        onClose={() => setIsAddEditOpen(false)}
        title={editMode === 'edit' ? 'Edit Offer' : 'Create New Offer'}
        subtitle={editMode === 'edit' && selectedOffer ? `Update ${selectedOffer.name} details` : 'Add a new promotional offer'}
        size="xl"
      >
        <OfferAddEdit
          offer={selectedOffer}
          onSave={handleSaveOffer}
          onCancel={() => setIsAddEditOpen(false)}
          mode={editMode}
        />
      </Modal>

      {/* View Modal */}
      <Modal
        isOpen={isViewOpen}
        onClose={() => setIsViewOpen(false)}
        title="Offer Details"
        subtitle={selectedOffer ? `${selectedOffer.name} (${selectedOffer.offer_code})` : ''}
        size="xl"
      >
        <OfferView
          offer={selectedOffer}
          onEdit={handleViewToEdit}
          onClose={() => setIsViewOpen(false)}
        />
      </Modal>
    </div>
  );
}
