


import apiService from '../api/api-service';
import {
  Coupon,
  CreateCouponRequest,
  UpdateCouponRequest,
  CouponFilters
} from './coupon.model';

class CouponService {

  // Get all coupons - API returns simple array, implement client-side filtering
  async getAllCoupons(filters?: CouponFilters): Promise<Coupon[]> {
    try {
      // API returns simple array of coupons
      const response = await apiService.getcoupons<Coupon[]>('/v1/coupons');

      let filteredCoupons = response;

      // Apply filters to API data
      if (filters) {
        if (filters.search) {
          const searchTerm = filters.search.toLowerCase();
          filteredCoupons = filteredCoupons.filter(coupon =>
            coupon.name.toLowerCase().includes(searchTerm) ||
            coupon.coupon_code.toLowerCase().includes(searchTerm) ||
            (coupon.description && coupon.description.toLowerCase().includes(searchTerm))
          );
        }
        if (filters.coupon_type) {
          filteredCoupons = filteredCoupons.filter(coupon => coupon.coupon_type === filters.coupon_type);
        }
        if (filters.business_type) {
          filteredCoupons = filteredCoupons.filter(coupon => coupon.business_type === filters.business_type);
        }
        if (filters.service_type) {
          filteredCoupons = filteredCoupons.filter(coupon => coupon.service_type === filters.service_type);
        }
      }

      return filteredCoupons;
    } catch (error) {
      console.error('Error fetching coupons:', error);
      throw error; // Re-throw the error for the caller to handle
    }
  }

  // Get single coupon by ID
  async getCouponById(id: number): Promise<Coupon> {
    try {
      const response = await apiService.getcoupons<Coupon>(`/v1/coupons/${id}`);
      return response;
    } catch (error) {
      console.error(`Error fetching coupon with ID ${id}:`, error);
      throw error;
    }
  }

  // Create new coupon
  async createCoupon(couponData: CreateCouponRequest): Promise<Coupon> {
    try {
      const response = await apiService.postcoupons<Coupon>('/v1/coupons', couponData);
      return response;
    } catch (error) {
      console.error('Error creating coupon:', error);
      throw error;
    }
  }

  // Update existing coupon
  async updateCoupon(id: number, couponData: UpdateCouponRequest): Promise<Coupon> {
    try {
      const response = await apiService.putcoupons<Coupon>(`/v1/coupons/${id}`, couponData);
      return response;
    } catch (error) {
      console.error(`Error updating coupon with ID ${id}:`, error);
      throw error;
    }
  }

  // Delete coupon
  async deleteCoupon(id: number): Promise<void> {
    try {
      await apiService.deletecoupons<void>(`/v1/coupons/${id}`);
    } catch (error) {
      console.error(`Error deleting coupon with ID ${id}:`, error);
      throw error;
    }
  }
}

const couponService = new CouponService();
export default couponService;