// 'use client';

// import React from 'react';

// interface AppStoreLink {
//   platform: 'ios' | 'android';
//   url: string;
//   enabled: boolean;
// }

// interface Props {
//   appStoreLinks: AppStoreLink[];
//   onToggle: (platform: 'ios' | 'android') => void;
//   onUpdate: (platform: 'ios' | 'android', url: string) => void;
// }

// const AppStoreLinks: React.FC<Props> = ({ appStoreLinks, onToggle, onUpdate }) => {
//   return (
//     <div className="animate-fade-in bg-white rounded-lg shadow-sm border border-gray-200">
//       <div className="p-6 border-b border-gray-200">
//         <div className="flex items-center space-x-3">
//           <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
//             <i className="ri-smartphone-line text-blue-600 text-lg"></i>
//           </div>
//           <div>
//             <h2 className="text-lg font-semibold text-gray-900">App Store Links</h2>
//             <p className="text-sm text-gray-600">Manage mobile application download links</p>
//           </div>
//         </div>
//       </div>

//       <div className="p-6 space-y-4">
//         {appStoreLinks.map((link) => (
//           <div key={link.platform} className="flex items-center space-x-4 p-4 bg-gray-50 rounded-lg border border-gray-200">
//             <div
//               className={`w-12 h-12 rounded-lg flex items-center justify-center ${
//                 link.platform === 'ios' ? 'bg-black' : 'bg-green-500'
//               }`}
//             >
//               <i
//                 className={`${
//                   link.platform === 'ios' ? 'ri-apple-fill' : 'ri-google-play-fill'
//                 } text-white text-xl`}
//               ></i>
//             </div>
//             <div className="flex-1">
//               <label className="block text-sm font-medium text-gray-700 mb-2">
//                 {link.platform === 'ios' ? 'App Store' : 'Play Store'} URL
//               </label>
//               <input
//                 type="url"
//                 value={link.url}
//                 onChange={(e) => onUpdate(link.platform, e.target.value)}
//                 placeholder={`Enter ${link.platform === 'ios' ? 'App Store' : 'Play Store'} URL`}
//                 className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
//               />
//             </div>
//             <div className="flex items-center">
//               <button
//                 onClick={() => onToggle(link.platform)}
//                 className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
//                   link.enabled ? 'bg-purple-600' : 'bg-gray-300'
//                 }`}
//               >
//                 <span
//                   className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
//                     link.enabled ? 'translate-x-6' : 'translate-x-1'
//                   }`}
//                 />
//               </button>
//               <span className="ml-3 text-sm font-medium text-gray-700">
//                 {link.enabled ? 'Enabled' : 'Disabled'}
//               </span>
//             </div>
//           </div>
//         ))}
//       </div>
//     </div>
//   );
// };

// export default AppStoreLinks;

'use client';

import React from 'react';

interface AppStoreLink {
  platform: 'ios' | 'android';
  url: string;
  enabled: boolean;
}

interface Props {
  appStoreLinks: AppStoreLink[];
  onToggle: (platform: 'ios' | 'android') => void;
  onUpdate: (platform: 'ios' | 'android', url: string) => void;
}

const AppStoreLinks: React.FC<Props> = ({ appStoreLinks, onToggle, onUpdate }) => {
  return (
    <div className="animate-fade-in bg-white rounded-lg shadow border border-gray-200">
      {/* Header */}
      <div className="p-6 border-b border-gray-200 flex items-center space-x-3">
        <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
          <i className="ri-smartphone-line text-blue-600 text-lg"></i>
        </div>
        <div>
          <h2 className="text-lg font-semibold text-gray-900">App Store Links</h2>
          <p className="text-sm text-gray-600">Manage mobile application download links</p>
        </div>
      </div>

      {/* Links */}
      <div className="p-6 space-y-4">
        {appStoreLinks.map((link) => (
          <div
            key={link.platform}
            className="flex items-center space-x-4 p-4 bg-gray-50 rounded-lg border border-gray-200"
          >
            {/* Platform Icon */}
            <div
              className={`w-12 h-12 rounded-lg flex items-center justify-center ${
                link.platform === 'ios' ? 'bg-black' : 'bg-green-500'
              }`}
            >
              <i
                className={`${
                  link.platform === 'ios' ? 'ri-apple-fill' : 'ri-google-play-fill'
                } text-white text-xl`}
              ></i>
            </div>

            {/* URL Input */}
            <div className="flex-1">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {link.platform === 'ios' ? 'App Store' : 'Play Store'} URL
              </label>
              <input
                type="url"
                value={link.url}
                onChange={(e) => onUpdate(link.platform, e.target.value)}
                placeholder={`Enter ${link.platform === 'ios' ? 'App Store' : 'Play Store'} URL`}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
              />
            </div>

            {/* Toggle */}
            <div className="flex items-center">
              <button
                onClick={() => onToggle(link.platform)}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                  link.enabled ? 'bg-purple-600' : 'bg-gray-300'
                }`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    link.enabled ? 'translate-x-6' : 'translate-x-1'
                  }`}
                />
              </button>
              <span className="ml-3 text-sm font-medium text-gray-700">
                {link.enabled ? 'Enabled' : 'Disabled'}
              </span>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default AppStoreLinks;
