import apiService from '../api/api-service';
import {
  Offer,
  CreateOfferRequest,
  UpdateOfferRequest,
  OfferFilters
} from './offer.model';

// Mock data for fallback when API is not available
const mockOffers: Offer[] = [
  {
    id: 1,
    name: "Holiday Special Offer",
    created_at: "2025-01-01T00:00:00Z",
    updated_at: "2025-01-01T00:00:00Z",
    deleted_at: null,
    created_by: "admin",
    updated_by: "admin",
    offer_type: "PERCENTAGE",
    offer_value: 20,
    business_type: "B2C",
    service_type: "HOTEL",
    user_id: null,
    offer_code: "HOLIDAY20",
    image: "https://example.com/holiday-offer.png",
    description: "20% off on all hotel bookings for holiday season",
    terms_and_conditions: "Valid for bookings above $300. Cannot be combined with other offers.",
    valid_from: "2025-01-01T00:00:00Z",
    valid_to: "2025-01-31T23:59:59Z",
    flights: null,
    hotels: null
  },
  {
    id: 2,
    name: "Early Bird Flight Deal",
    created_at: "2025-06-01T00:00:00Z",
    updated_at: "2025-06-01T00:00:00Z",
    deleted_at: null,
    created_by: "manager",
    updated_by: "manager",
    offer_type: "FIXED_AMOUNT",
    offer_value: 75,
    business_type: "B2B",
    service_type: "FLIGHT",
    user_id: null,
    offer_code: "EARLYBIRD75",
    image: null,
    description: "$75 off on early bird flight bookings",
    terms_and_conditions: "Valid for international flights only. Book 60 days in advance.",
    valid_from: "2025-06-01T00:00:00Z",
    valid_to: "2025-08-31T23:59:59Z",
    flights: null,
    hotels: null
  },
  {
    id: 3,
    name: "Premium Package Upgrade",
    created_at: "2025-03-15T00:00:00Z",
    updated_at: "2025-03-15T00:00:00Z",
    deleted_at: null,
    created_by: "staff",
    updated_by: "staff",
    offer_type: "FREE_UPGRADE",
    offer_value: 0,
    business_type: "B2C",
    service_type: "PACKAGE",
    user_id: 456,
    offer_code: "PREMIUM2025",
    image: "https://example.com/premium-offer.png",
    description: "Free upgrade to premium package",
    terms_and_conditions: "Valid for select packages only. User-specific offer.",
    valid_from: null,
    valid_to: "2025-12-31T23:59:59Z",
    flights: null,
    hotels: null
  }
];

class OfferService {

  // Get all offers - API returns simple array, implement client-side pagination
  async getAllOffers(filters?: OfferFilters): Promise<Offer[]> {
    try {
      // API returns simple array of offers, no pagination parameters needed
      const response = await apiService.getoffers<Offer[]>('/v1/offers');

      let filteredOffers = response;

      // Apply filters to API data
      if (filters) {
        if (filters.search) {
          const searchTerm = filters.search.toLowerCase();
          filteredOffers = filteredOffers.filter(offer =>
            offer.name.toLowerCase().includes(searchTerm) ||
            offer.offer_code.toLowerCase().includes(searchTerm) ||
            offer.description.toLowerCase().includes(searchTerm)
          );
        }
        if (filters.offer_type) {
          filteredOffers = filteredOffers.filter(offer => offer.offer_type === filters.offer_type);
        }
        if (filters.business_type) {
          filteredOffers = filteredOffers.filter(offer => offer.business_type === filters.business_type);
        }
        if (filters.service_type) {
          filteredOffers = filteredOffers.filter(offer => offer.service_type === filters.service_type);
        }
      }

      return filteredOffers;
    } catch (error) {
      console.error('Error fetching offers:', error);

      // Fallback to mock data
      console.log('Using fallback mock data for offers list');
      let filteredOffers = [...mockOffers];

      // Apply filters to mock data
      if (filters) {
        if (filters.search) {
          const searchTerm = filters.search.toLowerCase();
          filteredOffers = filteredOffers.filter(offer =>
            offer.name.toLowerCase().includes(searchTerm) ||
            offer.offer_code.toLowerCase().includes(searchTerm) ||
            offer.description.toLowerCase().includes(searchTerm)
          );
        }
        if (filters.offer_type) {
          filteredOffers = filteredOffers.filter(offer => offer.offer_type === filters.offer_type);
        }
        if (filters.business_type) {
          filteredOffers = filteredOffers.filter(offer => offer.business_type === filters.business_type);
        }
        if (filters.service_type) {
          filteredOffers = filteredOffers.filter(offer => offer.service_type === filters.service_type);
        }
      }

      return filteredOffers;
    }
  }

  // Get single offer by ID
  async getOfferById(id: number): Promise<Offer> {
    try {
      const response = await apiService.getoffers<Offer>(`/v1/offers/${id}`);
      return response;
    } catch (error) {
      console.error('Error fetching offer:', error);

      // Fallback to mock data
      console.log(`Using fallback mock data for offer ID: ${id}`);
      const mockOffer = mockOffers.find(offer => offer.id === id);
      if (mockOffer) {
        return mockOffer;
      }
      throw new Error('Offer not found');
    }
  }

  // Create new offer
  async createOffer(offerData: CreateOfferRequest): Promise<Offer> {
    try {
      const response = await apiService.postoffers<Offer>('/v1/offers', offerData);
      return response;
    } catch (error) {
      console.error('Error creating offer:', error);

      // Fallback to mock creation
      console.log('Using fallback mock data for offer creation');
      const newOffer: Offer = {
        id: Math.max(...mockOffers.map(o => o.id)) + 1,
        name: offerData.name,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        deleted_at: null,
        created_by: "mock_user",
        updated_by: "mock_user",
        offer_type: offerData.offer_type,
        offer_value: offerData.offer_value,
        business_type: offerData.business_type,
        service_type: offerData.service_type,
        user_id: offerData.user_id || null,
        offer_code: offerData.offer_code,
        image: offerData.image || null,
        description: offerData.description,
        terms_and_conditions: offerData.terms_and_conditions || null,
        valid_from: offerData.valid_from || null,
        valid_to: offerData.valid_to || null,
        flights: null,
        hotels: null
      };

      // Add to mock data (in real app, this would be persisted)
      mockOffers.push(newOffer);
      return newOffer;
    }
  }

  // Update existing offer
  async updateOffer(id: number, offerData: UpdateOfferRequest): Promise<Offer> {
    try {
      const response = await apiService.putoffers<Offer>(`/v1/offers/${id}`, offerData);
      return response;
    } catch (error) {
      console.error('Error updating offer:', error);

      // Fallback to mock update
      console.log(`Using fallback mock data for offer update ID: ${id}`);
      const offerIndex = mockOffers.findIndex(offer => offer.id === id);
      if (offerIndex === -1) {
        throw new Error('Offer not found');
      }

      const updatedOffer: Offer = {
        ...mockOffers[offerIndex],
        ...offerData,
        updated_at: new Date().toISOString(),
        updated_by: "mock_user"
      };

      mockOffers[offerIndex] = updatedOffer;
      return updatedOffer;
    }
  }

  // Delete offer
  async deleteOffer(id: number): Promise<void> {
    try {
      await apiService.deleteoffers<void>(`/v1/offers/${id}`);
    } catch (error) {
      console.error('Error deleting offer:', error);

      // Fallback to mock deletion
      console.log(`Using fallback mock data for offer deletion ID: ${id}`);
      const offerIndex = mockOffers.findIndex(offer => offer.id === id);
      if (offerIndex === -1) {
        throw new Error('Offer not found');
      }

      // Mark as deleted (soft delete)
      mockOffers[offerIndex].deleted_at = new Date().toISOString();
    }
  }




}

const offerService = new OfferService();
export default offerService;
