// 'use client';

// import { useState, useEffect } from 'react';
// import MarkupMaster from './components/MarkupMaster';

// export default function MarkupPage() {
//   const [lastSyncTime, setLastSyncTime] = useState('--:--:--');

//   useEffect(() => {
//     setLastSyncTime(new Date().toLocaleTimeString());
//   }, []);

//   return (
//     <div className="h-full flex flex-col">
//       {/* Enhanced Professional Header */}
//       <div className="flex-shrink-0 bg-white border-b border-gray-200">
//         <div className="px-6 py-6">
//           <div className="flex flex-col space-y-4 lg:flex-row lg:items-center lg:justify-between lg:space-y-0">
//             <div className="flex-1 min-w-0">
//               <div className="flex items-center space-x-3 mb-2">
//                 <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center">
//                   <i className="ri-price-tag-3-line text-white text-lg"></i>
//                 </div>
//                 <div>
//                   <h1 className="text-2xl font-bold text-gray-900">
//                     Markup Management
//                   </h1>
//                   <p className="text-sm text-gray-600">
//                     Manage pricing markups and provider configurations
//                   </p>
//                 </div>
//               </div>
//             </div>

//             {/* Action Buttons */}
//             <div className="flex items-center space-x-3">
//               <div className="flex items-center space-x-2 text-sm text-gray-500">
//                 <i className="ri-refresh-line"></i>
//                 <span>Last sync: {lastSyncTime}</span>
//               </div>
              
//               <div className="h-6 w-px bg-gray-300"></div>
              
//               <button className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors">
//                 <i className="ri-download-line mr-2"></i>
//                 Export
//               </button>

//               <button className="inline-flex items-center px-4 py-2 border border-transparent rounded-lg text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors">
//                 <i className="ri-add-line mr-2"></i>
//                 Add Markup
//               </button>
//             </div>
//           </div>

//           {/* Stats Cards */}
//           <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mt-6">
//             <div className="bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg p-4 border border-blue-200">
//               <div className="flex items-center">
//                 <div className="flex-shrink-0">
//                   <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
//                     <i className="ri-price-tag-line text-white text-sm"></i>
//                   </div>
//                 </div>
//                 <div className="ml-3">
//                   <p className="text-sm font-medium text-blue-900">Total Markups</p>
//                   <p className="text-lg font-semibold text-blue-700">24</p>
//                 </div>
//               </div>
//             </div>

//             <div className="bg-gradient-to-r from-green-50 to-green-100 rounded-lg p-4 border border-green-200">
//               <div className="flex items-center">
//                 <div className="flex-shrink-0">
//                   <div className="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center">
//                     <i className="ri-check-line text-white text-sm"></i>
//                   </div>
//                 </div>
//                 <div className="ml-3">
//                   <p className="text-sm font-medium text-green-900">Active</p>
//                   <p className="text-lg font-semibold text-green-700">18</p>
//                 </div>
//               </div>
//             </div>

//             <div className="bg-gradient-to-r from-yellow-50 to-yellow-100 rounded-lg p-4 border border-yellow-200">
//               <div className="flex items-center">
//                 <div className="flex-shrink-0">
//                   <div className="w-8 h-8 bg-yellow-500 rounded-lg flex items-center justify-center">
//                     <i className="ri-pause-line text-white text-sm"></i>
//                   </div>
//                 </div>
//                 <div className="ml-3">
//                   <p className="text-sm font-medium text-yellow-900">Inactive</p>
//                   <p className="text-lg font-semibold text-yellow-700">6</p>
//                 </div>
//               </div>
//             </div>

//             <div className="bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg p-4 border border-blue-200">
//               <div className="flex items-center">
//                 <div className="flex-shrink-0">
//                   <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
//                     <i className="ri-percent-line text-white text-sm"></i>
//                   </div>
//                 </div>
//                 <div className="ml-3">
//                   <p className="text-sm font-medium text-blue-900">Avg Markup</p>
//                   <p className="text-lg font-semibold text-blue-700">12.5%</p>
//                 </div>
//               </div>
//             </div>
//           </div>
//         </div>
//       </div>

//       {/* Content Area with Internal Scrolling */}
//       <div className="flex-1 overflow-y-auto custom-scrollbar bg-gray-50">
//         <div className="p-6">
//           <div className="max-w-full">
//             <div className="animate-fade-in">
//               <MarkupMaster />
//             </div>
//           </div>
//         </div>
//       </div>
//     </div>
//   );
// }



'use client';

import { useState, useEffect } from 'react';

// Mock Markup type based on the provided model
interface Markup {
  id: string;
  name: string;
  providerType: 'airline' | 'hotel' | 'common markup';
  status: 'active' | 'inactive';
  type: 'percentage' | 'rate';
  value: number;
  currency?: string;
  description?: string;
  updatedAt: string;
}

export default function MarkupPage() {
  const [lastSyncTime, setLastSyncTime] = useState('--:--:--');
  const [activeTab, setActiveTab] = useState('airline');
  
  // Mock data for all markups
  const [allMarkups, setAllMarkups] = useState<Markup[]>([
    // Airline markups
    { id: 'airline_1', name: 'Emirates Standard', providerType: 'airline', type: 'percentage', value: 15, status: 'active', updatedAt: '2024-10-10', description: 'Standard markup for Emirates' },
    { id: 'airline_2', name: 'Qatar Airways Premium', providerType: 'airline', type: 'percentage', value: 12, status: 'active', updatedAt: '2024-10-12', description: 'Premium tier markup' },
    { id: 'airline_3', name: 'Etihad Commission', providerType: 'airline', type: 'rate', value: 50, currency: 'USD', status: 'active', updatedAt: '2024-10-08', description: 'Fixed commission rate' },
    { id: 'airline_4', name: 'Air India Seasonal', providerType: 'airline', type: 'percentage', value: 10, status: 'inactive', updatedAt: '2024-09-25', description: 'Seasonal markup inactive' },
    { id: 'airline_5', name: 'British Airways', providerType: 'airline', type: 'percentage', value: 18, status: 'active', updatedAt: '2024-10-14', description: 'BA standard commission' },
    { id: 'airline_6', name: 'Lufthansa Group', providerType: 'airline', type: 'rate', value: 75, currency: 'EUR', status: 'active', updatedAt: '2024-10-11', description: 'Lufthansa group rate' },
    
    // Hotel markups
    { id: 'hotel_1', name: 'Marriott Hotels', providerType: 'hotel', type: 'percentage', value: 20, status: 'active', updatedAt: '2024-10-13', description: 'Marriott standard markup' },
    { id: 'hotel_2', name: 'Hilton Worldwide', providerType: 'hotel', type: 'percentage', value: 18, status: 'active', updatedAt: '2024-10-09', description: 'Hilton commission' },
    { id: 'hotel_3', name: 'Hyatt Regency', providerType: 'hotel', type: 'rate', value: 100, currency: 'USD', status: 'active', updatedAt: '2024-10-15', description: 'Fixed booking fee' },
    { id: 'hotel_4', name: 'Radisson Collection', providerType: 'hotel', type: 'percentage', value: 15, status: 'inactive', updatedAt: '2024-09-30', description: 'Temporarily disabled' },
    { id: 'hotel_5', name: 'ITC Hotels India', providerType: 'hotel', type: 'percentage', value: 22, status: 'active', updatedAt: '2024-10-12', description: 'ITC premium markup' },
    { id: 'hotel_6', name: 'Taj Hotels Resorts', providerType: 'hotel', type: 'rate', value: 120, currency: 'USD', status: 'active', updatedAt: '2024-10-10', description: 'Taj booking commission' },
    
    // Common markups
    { id: 'common_1', name: 'Visa Services', providerType: 'common markup', type: 'percentage', value: 8, status: 'active', updatedAt: '2024-10-14', description: 'Visa processing fee' },
    { id: 'common_2', name: 'Travel Insurance', providerType: 'common markup', type: 'percentage', value: 12, status: 'active', updatedAt: '2024-10-11', description: 'Insurance commission' },
    { id: 'common_3', name: 'Airport Transfer', providerType: 'common markup', type: 'rate', value: 25, currency: 'USD', status: 'active', updatedAt: '2024-10-13', description: 'Transfer service fee' },
    { id: 'common_4', name: 'Car Rental', providerType: 'common markup', type: 'percentage', value: 10, status: 'inactive', updatedAt: '2024-09-28', description: 'Car rental markup' },
    { id: 'common_5', name: 'Tour Packages', providerType: 'common markup', type: 'percentage', value: 15, status: 'active', updatedAt: '2024-10-15', description: 'Tour package commission' },
    { id: 'common_6', name: 'Currency Exchange', providerType: 'common markup', type: 'rate', value: 30, currency: 'USD', status: 'active', updatedAt: '2024-10-09', description: 'FX service fee' },
  ]);
  
  // Search states for each tab
  const [airlineSearch, setAirlineSearch] = useState({
    name: '',
    type: 'all',
    value: ''
  });
  
  const [hotelSearch, setHotelSearch] = useState({
    name: '',
    type: 'all',
    value: ''
  });
  
  const [commonSearch, setCommonSearch] = useState({
    name: '',
    type: 'all',
    value: ''
  });

  useEffect(() => {
    setLastSyncTime(new Date().toLocaleTimeString());
  }, []);

  // Filter functions
  const getFilteredAirlines = () => {
    return allMarkups.filter(m => {
      const matchesType = m.providerType === 'airline';
      const matchesName = !airlineSearch.name || m.name.toLowerCase().includes(airlineSearch.name.toLowerCase());
      const matchesMarkupType = airlineSearch.type === 'all' || m.type === airlineSearch.type;
      const matchesValue = !airlineSearch.value || m.value.toString() === airlineSearch.value;
      return matchesType && matchesName && matchesMarkupType && matchesValue;
    });
  };

  const getFilteredHotels = () => {
    return allMarkups.filter(m => {
      const matchesType = m.providerType === 'hotel';
      const matchesName = !hotelSearch.name || m.name.toLowerCase().includes(hotelSearch.name.toLowerCase());
      const matchesMarkupType = hotelSearch.type === 'all' || m.type === hotelSearch.type;
      const matchesValue = !hotelSearch.value || m.value.toString() === hotelSearch.value;
      return matchesType && matchesName && matchesMarkupType && matchesValue;
    });
  };

  const getFilteredCommon = () => {
    return allMarkups.filter(m => {
      const matchesType = m.providerType === 'common markup';
      const matchesName = !commonSearch.name || m.name.toLowerCase().includes(commonSearch.name.toLowerCase());
      const matchesMarkupType = commonSearch.type === 'all' || m.type === commonSearch.type;
      const matchesValue = !commonSearch.value || m.value.toString() === commonSearch.value;
      return matchesType && matchesName && matchesMarkupType && matchesValue;
    });
  };

  const handleSearch = (tabType: string) => {
    console.log(`Searching ${tabType}...`);
  };

  const handleEdit = (markup: Markup) => {
    console.log('Edit markup:', markup);
    alert(`Edit: ${markup.name}`);
  };

  const handleDelete = (markup: Markup) => {
    if (confirm(`Are you sure you want to delete "${markup.name}"?`)) {
      setAllMarkups(prev => prev.filter(m => m.id !== markup.id));
      setLastSyncTime(new Date().toLocaleTimeString());
    }
  };

  const handleAddMarkup = () => {
    alert('Add new markup functionality');
  };

  const tabs = [
    { id: 'airline', label: 'Airline Markup', icon: 'ri-flight-takeoff-line', description: 'Manage airline pricing markups' },
    { id: 'hotel', label: 'Hotel Markup', icon: 'ri-hotel-line', description: 'Manage hotel pricing markups' },
    { id: 'common', label: 'Common Markup', icon: 'ri-settings-3-line', description: 'Manage common service markups' }
  ];

  const renderTable = (data: Markup[]) => (
    <div className="mt-6 bg-white rounded-lg border border-gray-200 overflow-hidden">
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Name
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Type
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Value
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Last Updated
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {data.length > 0 ? (
              data.map((item) => (
                <tr key={item.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className={`flex-shrink-0 w-8 h-8 rounded-lg flex items-center justify-center ${
                        item.providerType === 'airline' ? 'bg-blue-100' :
                        item.providerType === 'hotel' ? 'bg-green-100' : 'bg-indigo-100'
                      }`}>
                        <i className={`${
                          item.providerType === 'airline' ? 'ri-flight-takeoff-fill text-blue-600' :
                          item.providerType === 'hotel' ? 'ri-hotel-fill text-green-600' : 
                          'ri-settings-3-fill text-indigo-600'
                        }`}></i>
                      </div>
                      <div className="ml-3">
                        <div className="text-sm font-medium text-gray-900">{item.name}</div>
                        {item.description && (
                          <div className="text-xs text-gray-500">{item.description}</div>
                        )}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                      item.type === 'percentage' 
                        ? 'bg-purple-100 text-purple-800' 
                        : 'bg-orange-100 text-orange-800'
                    }`}>
                      {item.type === 'percentage' ? 'Percentage' : 'Rate'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-semibold text-gray-900">
                    {item.type === 'percentage' ? `${item.value}%` : `${item.currency || '$'}${item.value}`}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                      item.status === 'active' 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {item.status.charAt(0).toUpperCase() + item.status.slice(1)}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {item.updatedAt}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <button 
                      onClick={() => handleEdit(item)}
                      className="text-blue-600 hover:text-blue-900 mr-3"
                      title="Edit"
                    >
                      <i className="ri-edit-line text-lg"></i>
                    </button>
                    <button 
                      onClick={() => handleDelete(item)}
                      className="text-red-600 hover:text-red-900"
                      title="Delete"
                    >
                      <i className="ri-delete-bin-line text-lg"></i>
                    </button>
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={6} className="px-6 py-8 text-center text-gray-500">
                  No results found. Try adjusting your search criteria.
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    </div>
  );

  return (
    <div className="h-screen flex flex-col bg-gray-50">
      {/* Enhanced Professional Header */}
      <div className="flex-shrink-0 bg-white border-b border-gray-200">
        <div className="px-6 py-6">
          <div className="flex flex-col space-y-4 lg:flex-row lg:items-center lg:justify-between lg:space-y-0">
            <div className="flex-1 min-w-0">
              <div className="flex items-center space-x-3 mb-2">
                <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center">
                  <i className="ri-price-tag-3-line text-white text-lg"></i>
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">
                    Markup Management
                  </h1>
                  <p className="text-sm text-gray-600">
                    Manage pricing markups and provider configurations
                  </p>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex items-center space-x-3">
              <div className="flex items-center space-x-2 text-sm text-gray-500">
                <i className="ri-refresh-line"></i>
                <span>Last sync: {lastSyncTime}</span>
              </div>
              
              <div className="h-6 w-px bg-gray-300"></div>
              
              <button className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors">
                <i className="ri-download-line mr-2"></i>
                Export
              </button>

              <button 
                onClick={handleAddMarkup}
                className="inline-flex items-center px-4 py-2 border border-transparent rounded-lg text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 transition-colors"
              >
                <i className="ri-add-line mr-2"></i>
                Add Markup
              </button>
            </div>
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mt-6">
            <div className="bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg p-4 border border-blue-200">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
                    <i className="ri-price-tag-line text-white text-sm"></i>
                  </div>
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-blue-900">Total Markups</p>
                  <p className="text-lg font-semibold text-blue-700">{allMarkups.length}</p>
                </div>
              </div>
            </div>

            <div className="bg-gradient-to-r from-green-50 to-green-100 rounded-lg p-4 border border-green-200">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center">
                    <i className="ri-check-line text-white text-sm"></i>
                  </div>
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-green-900">Active</p>
                  <p className="text-lg font-semibold text-green-700">
                    {allMarkups.filter(m => m.status === 'active').length}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-gradient-to-r from-yellow-50 to-yellow-100 rounded-lg p-4 border border-yellow-200">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-yellow-500 rounded-lg flex items-center justify-center">
                    <i className="ri-pause-line text-white text-sm"></i>
                  </div>
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-yellow-900">Inactive</p>
                  <p className="text-lg font-semibold text-yellow-700">
                    {allMarkups.filter(m => m.status === 'inactive').length}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-gradient-to-r from-blue-50 to-blue-100 rounded-lg p-4 border border-blue-200">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
                    <i className="ri-percent-line text-white text-sm"></i>
                  </div>
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-blue-900">Avg Markup</p>
                  <p className="text-lg font-semibold text-blue-700">
                    {(allMarkups.filter(m => m.type === 'percentage').reduce((sum, m) => sum + m.value, 0) / 
                      allMarkups.filter(m => m.type === 'percentage').length).toFixed(1)}%
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Content Area with Tabs */}
      <div className="flex-1 overflow-y-auto">
        <div className="flex-shrink-0 bg-white border-b border-gray-200">
          <div className="px-6">
            <nav className="flex overflow-x-auto">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`relative px-6 py-4 font-medium text-sm transition-all duration-200 border-b-2 whitespace-nowrap min-w-max ${
                    activeTab === tab.id
                      ? 'text-blue-600 border-blue-600 bg-blue-50/50'
                      : 'text-gray-600 border-transparent hover:text-gray-900 hover:bg-gray-50'
                  }`}
                >
                  <div className="flex items-center space-x-3">
                    <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${
                      activeTab === tab.id ? 'bg-blue-100' : 'bg-gray-100'
                    }`}>
                      <i className={`${tab.icon} text-base ${
                        activeTab === tab.id ? 'text-blue-600' : 'text-gray-600'
                      }`}></i>
                    </div>
                    <div className="text-left">
                      <div className="font-semibold">{tab.label}</div>
                      <div className="text-xs font-normal opacity-75 hidden sm:block">{tab.description}</div>
                    </div>
                  </div>
                </button>
              ))}
            </nav>
          </div>
        </div>

        <div className="p-6">
          {/* Tab Content */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            <div className="p-6">
              {/* Airline Markup Tab */}
              {activeTab === 'airline' && (
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">
                    Search Airline Markup
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Airline Name
                      </label>
                      <input
                        type="text"
                        value={airlineSearch.name}
                        onChange={(e) => setAirlineSearch({ ...airlineSearch, name: e.target.value })}
                        placeholder="Enter airline name"
                        className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Type
                      </label>
                      <select
                        value={airlineSearch.type}
                        onChange={(e) => setAirlineSearch({ ...airlineSearch, type: e.target.value })}
                        className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      >
                        <option value="all">All Types</option>
                        <option value="percentage">Percentage</option>
                        <option value="rate">Rate</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Value
                      </label>
                      <input
                        type="number"
                        value={airlineSearch.value}
                        onChange={(e) => setAirlineSearch({ ...airlineSearch, value: e.target.value })}
                        placeholder="Enter value"
                        className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                    <div className="flex items-end">
                      <button
                        onClick={() => handleSearch('airline')}
                        className="w-full px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center"
                      >
                        <i className="ri-search-line mr-2"></i>
                        Search
                      </button>
                    </div>
                  </div>
                  
                  {renderTable(getFilteredAirlines())}
                </div>
              )}

              {/* Hotel Markup Tab */}
              {activeTab === 'hotel' && (
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">
                    Search Hotel Markup
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Hotel Name
                      </label>
                      <input
                        type="text"
                        value={hotelSearch.name}
                        onChange={(e) => setHotelSearch({ ...hotelSearch, name: e.target.value })}
                        placeholder="Enter hotel name"
                        className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Type
                      </label>
                      <select
                        value={hotelSearch.type}
                        onChange={(e) => setHotelSearch({ ...hotelSearch, type: e.target.value })}
                        className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      >
                        <option value="all">All Types</option>
                        <option value="percentage">Percentage</option>
                        <option value="rate">Rate</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Value
                      </label>
                      <input
                        type="number"
                        value={hotelSearch.value}
                        onChange={(e) => setHotelSearch({ ...hotelSearch, value: e.target.value })}
                        placeholder="Enter value"
                        className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                    <div className="flex items-end">
                      <button
                        onClick={() => handleSearch('hotel')}
                        className="w-full px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center"
                      >
                        <i className="ri-search-line mr-2"></i>
                        Search
                      </button>
                    </div>
                  </div>
                  
                  {renderTable(getFilteredHotels())}
                </div>
              )}

              {/* Common Markup Tab */}
              {activeTab === 'common' && (
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">
                    Search Common Markup
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Service Name
                      </label>
                      <input
                        type="text"
                        value={commonSearch.name}
                        onChange={(e) => setCommonSearch({ ...commonSearch, name: e.target.value })}
                        placeholder="Enter service name"
                        className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Type
                      </label>
                      <select
                        value={commonSearch.type}
                        onChange={(e) => setCommonSearch({ ...commonSearch, type: e.target.value })}
                        className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      >
                        <option value="all">All Types</option>
                        <option value="percentage">Percentage</option>
                        <option value="rate">Rate</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Value
                      </label>
                      <input
                        type="number"
                        value={commonSearch.value}
                        onChange={(e) => setCommonSearch({ ...commonSearch, value: e.target.value })}
                        placeholder="Enter value"
                        className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                    <div className="flex items-end">
                      <button
                        onClick={() => handleSearch('common')}
                        className="w-full px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center"
                      >
                        <i className="ri-search-line mr-2"></i>
                        Search
                      </button>
                    </div>
                  </div>
                  
                  {renderTable(getFilteredCommon())}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}