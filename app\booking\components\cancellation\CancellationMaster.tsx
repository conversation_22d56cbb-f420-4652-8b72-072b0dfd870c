'use client';

import { useState, useEffect } from 'react';
import CancellationStats from './CancellationStats';
import CancellationList from './CancellationList';
import CancellationView from './CancellationView';
import { Cancellation, CancellationFilters } from '../types';

export default function CancellationMaster() {
  const [cancellations, setCancellations] = useState<Cancellation[]>([]);
  const [selectedCancellation, setSelectedCancellation] = useState<Cancellation | null>(null);
  const [isViewOpen, setIsViewOpen] = useState(false);
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(5);
  const [filters, setFilters] = useState<CancellationFilters>({
    search: '',
    cancellationStatus: '',
    refundStatus: '',
    cancellationReason: '',
    cancellationType: '',
    requestedBy: '',
    dateRange: {
      startDate: '',
      endDate: ''
    },
    hotelId: '',
    refundMethod: ''
  });

  useEffect(() => {
    // Mock data - in real app, fetch from API
    const fetchCancellations = async () => {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const mockCancellations: Cancellation[] = [
        {
          id: '1',
          cancellationId: 'CN-2024-001',
          bookingId: 'BK-2024-001',
          bookingReference: 'BK-2024-001',
          guestName: 'John Smith',
          guestEmail: '<EMAIL>',
          guestPhone: '******-0123',
          hotelId: '1',
          hotelName: 'Grand Palace Resort & Spa',
          roomId: '1',
          roomName: 'Deluxe Ocean View',
          originalCheckInDate: '2024-03-15',
          originalCheckOutDate: '2024-03-18',
          numberOfGuests: 2,
          numberOfNights: 3,
          cancellationDate: '2024-03-10T10:30:00Z',
          cancellationReason: 'guest-request',
          cancellationStatus: 'completed',
          cancellationType: 'full-cancellation',
          originalAmount: 750.00,
          refundAmount: 675.00,
          cancellationFee: 75.00,
          refundStatus: 'completed',
          refundMethod: 'original-payment',
          currency: 'USD',
          cancellationPolicy: 'Free cancellation up to 48 hours before check-in. 10% fee applies for cancellations within 48 hours.',
          requestedBy: 'guest',
          processedBy: 'Sarah Johnson',
          cancellationNotes: 'Guest requested cancellation due to change in travel plans. Processed according to standard policy.',
          refundNotes: 'Refund processed to original credit card ending in 4567. Transaction ID: REF-2024-001',
          createdAt: '2024-03-10T10:30:00Z',
          updatedAt: '2024-03-10T14:45:00Z',
          refundDate: '2024-03-10T14:45:00Z',
          refundTransactionId: 'REF-2024-001',
          guestDetails: {
            firstName: 'John',
            lastName: 'Smith',
            email: '<EMAIL>',
            phone: '******-0123',
            address: {
              street: '123 Main St',
              city: 'New York',
              state: 'NY',
              country: 'USA',
              zipCode: '10001'
            },
            nationality: 'USA'
          },
          paymentDetails: {
            originalTransactionId: 'TXN-2024-001',
            originalPaymentMethod: 'Credit Card',
            refundTransactionId: 'REF-2024-001',
            refundPaymentGateway: 'Stripe',
            cardLast4: '4567',
            cardType: 'Visa',
            refundReference: 'REF-2024-001'
          }
        },
        {
          id: '2',
          cancellationId: 'CN-2024-002',
          bookingId: 'BK-2024-002',
          bookingReference: 'BK-2024-002',
          guestName: 'Emily Davis',
          guestEmail: '<EMAIL>',
          guestPhone: '******-0124',
          hotelId: '2',
          hotelName: 'City Center Hotel',
          roomId: '2',
          roomName: 'Standard Double',
          originalCheckInDate: '2024-03-20',
          originalCheckOutDate: '2024-03-22',
          numberOfGuests: 1,
          numberOfNights: 2,
          cancellationDate: '2024-03-18T09:15:00Z',
          cancellationReason: 'medical-emergency',
          cancellationStatus: 'pending',
          cancellationType: 'full-cancellation',
          originalAmount: 320.00,
          refundAmount: 320.00,
          cancellationFee: 0.00,
          refundStatus: 'pending',
          refundMethod: 'original-payment',
          currency: 'USD',
          cancellationPolicy: 'Full refund for medical emergencies with documentation.',
          requestedBy: 'guest',
          processedBy: '',
          cancellationNotes: 'Medical emergency cancellation. Documentation provided.',
          refundNotes: '',
          createdAt: '2024-03-18T09:15:00Z',
          updatedAt: '2024-03-18T09:15:00Z',
          guestDetails: {
            firstName: 'Emily',
            lastName: 'Davis',
            email: '<EMAIL>',
            phone: '******-0124',
            nationality: 'USA'
          }
        },
        {
          id: '3',
          cancellationId: 'CN-2024-003',
          bookingId: 'BK-2024-003',
          bookingReference: 'BK-2024-003',
          guestName: 'Michael Brown',
          guestEmail: '<EMAIL>',
          guestPhone: '******-0125',
          hotelId: '1',
          hotelName: 'Grand Palace Resort & Spa',
          roomId: '3',
          roomName: 'Presidential Suite',
          originalCheckInDate: '2024-04-01',
          originalCheckOutDate: '2024-04-05',
          numberOfGuests: 4,
          numberOfNights: 4,
          cancellationDate: '2024-03-25T16:20:00Z',
          cancellationReason: 'travel-restrictions',
          cancellationStatus: 'processed',
          cancellationType: 'full-cancellation',
          originalAmount: 2400.00,
          refundAmount: 2160.00,
          cancellationFee: 240.00,
          refundStatus: 'processing',
          refundMethod: 'bank-transfer',
          currency: 'USD',
          cancellationPolicy: '10% cancellation fee applies for cancellations due to travel restrictions.',
          requestedBy: 'guest',
          processedBy: 'Mark Wilson',
          cancellationNotes: 'Cancellation due to government travel restrictions. 10% fee applied as per policy.',
          refundNotes: 'Bank transfer initiated. Processing time 3-5 business days.',
          createdAt: '2024-03-25T16:20:00Z',
          updatedAt: '2024-03-26T11:30:00Z',
          guestDetails: {
            firstName: 'Michael',
            lastName: 'Brown',
            email: '<EMAIL>',
            phone: '******-0125',
            nationality: 'UK'
          }
        }
      ];
      
      setCancellations(mockCancellations);
      setLoading(false);
    };

    fetchCancellations();
  }, []);



  const handleViewCancellation = (cancellation: Cancellation) => {
    setSelectedCancellation(cancellation);
    setIsViewOpen(true);
  };

  const handleViewClose = () => {
    setIsViewOpen(false);
    setSelectedCancellation(null);
  };



  // Filter cancellations based on current filters
  const filteredCancellations = cancellations.filter(cancellation => {
    const matchesSearch = !filters.search ||
      cancellation.cancellationId.toLowerCase().includes(filters.search.toLowerCase()) ||
      cancellation.bookingReference.toLowerCase().includes(filters.search.toLowerCase()) ||
      cancellation.guestName.toLowerCase().includes(filters.search.toLowerCase()) ||
      cancellation.guestEmail.toLowerCase().includes(filters.search.toLowerCase()) ||
      cancellation.hotelName.toLowerCase().includes(filters.search.toLowerCase());

    const matchesStatus = !filters.cancellationStatus || cancellation.cancellationStatus === filters.cancellationStatus;
    const matchesRefundStatus = !filters.refundStatus || cancellation.refundStatus === filters.refundStatus;
    const matchesReason = !filters.cancellationReason || cancellation.cancellationReason === filters.cancellationReason;

    return matchesSearch && matchesStatus && matchesRefundStatus && matchesReason;
  });

  // Pagination calculations
  const totalItems = filteredCancellations.length;
  const totalPages = Math.ceil(totalItems / pageSize);
  const startIndex = (currentPage - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const paginatedCancellations = filteredCancellations.slice(startIndex, endIndex);

  // Reset to page 1 when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [filters]);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handlePageSizeChange = (newPageSize: number) => {
    setPageSize(newPageSize);
    setCurrentPage(1);
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
            {[...Array(4)].map((_, index) => (
              <div key={index} className="bg-gray-200 rounded-lg h-32"></div>
            ))}
          </div>
          <div className="bg-gray-200 rounded-lg h-96"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Statistics Cards */}
      <CancellationStats />

      {/* Header with Actions */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Cancellations</h2>
          <p className="text-gray-600">Manage booking cancellations and refunds</p>
        </div>
        <div className="flex items-center space-x-3">
          <button className="inline-flex items-center px-4 py-2 bg-gray-100 text-gray-700 rounded-lg font-medium hover:bg-gray-200 transition-colors shadow-sm">
            <i className="ri-download-line mr-2"></i>
            Export Data
          </button>
          <button className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors shadow-sm">
            <i className="ri-refresh-line mr-2"></i>
            Refresh
          </button>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Search</label>
            <input
              type="text"
              value={filters.search}
              onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
              placeholder="Search cancellations..."
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Cancellation Status</label>
            <select
              value={filters.cancellationStatus}
              onChange={(e) => setFilters(prev => ({ ...prev, cancellationStatus: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">All Statuses</option>
              <option value="pending">Pending</option>
              <option value="approved">Approved</option>
              <option value="rejected">Rejected</option>
              <option value="processed">Processed</option>
              <option value="completed">Completed</option>
              <option value="cancelled">Cancelled</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Refund Status</label>
            <select
              value={filters.refundStatus}
              onChange={(e) => setFilters(prev => ({ ...prev, refundStatus: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">All Refund Statuses</option>
              <option value="not-applicable">Not Applicable</option>
              <option value="pending">Pending</option>
              <option value="processing">Processing</option>
              <option value="completed">Completed</option>
              <option value="failed">Failed</option>
              <option value="partial">Partial</option>
              <option value="rejected">Rejected</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Cancellation Reason</label>
            <select
              value={filters.cancellationReason}
              onChange={(e) => setFilters(prev => ({ ...prev, cancellationReason: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">All Reasons</option>
              <option value="guest-request">Guest Request</option>
              <option value="medical-emergency">Medical Emergency</option>
              <option value="travel-restrictions">Travel Restrictions</option>
              <option value="weather-conditions">Weather Conditions</option>
              <option value="hotel-issue">Hotel Issue</option>
              <option value="overbooking">Overbooking</option>
              <option value="no-show">No Show</option>
              <option value="duplicate-booking">Duplicate Booking</option>
              <option value="payment-failure">Payment Failure</option>
              <option value="force-majeure">Force Majeure</option>
              <option value="other">Other</option>
            </select>
          </div>
        </div>

        <div className="flex items-center justify-between mt-4">
          <div className="text-sm text-gray-600">
            Showing {filteredCancellations.length} of {cancellations.length} cancellations
          </div>
          <button
            onClick={() => setFilters({
              search: '',
              cancellationStatus: '',
              refundStatus: '',
              cancellationReason: '',
              cancellationType: '',
              requestedBy: '',
              dateRange: { startDate: '', endDate: '' },
              hotelId: '',
              refundMethod: ''
            })}
            className="text-sm text-blue-600 hover:text-blue-700 font-medium"
          >
            Clear Filters
          </button>
        </div>
      </div>

      {/* Cancellations List */}
      <CancellationList
        cancellations={paginatedCancellations}
        onView={handleViewCancellation}
        loading={false}
        currentPage={currentPage}
        totalPages={totalPages}
        pageSize={pageSize}
        totalItems={totalItems}
        onPageChange={handlePageChange}
        onPageSizeChange={handlePageSizeChange}
      />

      {/* View Modal */}
      <CancellationView
        cancellation={selectedCancellation}
        isOpen={isViewOpen}
        onClose={handleViewClose}
      />
    </div>
  );
}
