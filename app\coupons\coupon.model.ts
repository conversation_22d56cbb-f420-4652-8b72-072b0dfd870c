// Coupon Management Models and Types

// Enums for coupon properties
export type CouponType = 'PERCENTAGE' | 'FIXED';
export type BusinessType = 'B2B' | 'B2C' | 'BOTH';
export type ServiceType = 'HOTEL' | 'FLIGHT' | 'PACKAGE' | 'ALL';
export type CouponStatus = 'ACTIVE' | 'INACTIVE' | 'EXPIRED' | 'DRAFT';

// Main Coupon interface - matches API response structure
export interface Coupon {
  id: number;
  name: string;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
  created_by: string;
  updated_by: string;
  coupon_type: CouponType;
  coupon_value: number;
  business_type: BusinessType;
  service_type: ServiceType;
  user_id: number | null;
  coupon_code: string;
  image: string | null;
  description: string;
  terms_and_conditions: string | null;
  valid_from: string | null;
  valid_to: string | null;
  flights: any | null;
  hotels: any | null;
}

// Form data interface for create/update operations
export interface CouponFormData {
  name: string;
  coupon_type: CouponType;
  coupon_value: number;
  business_type: BusinessType;
  service_type: ServiceType;
  coupon_code: string;
  description: string;
  user_id?: number | null;
  image?: string | null;
  terms_and_conditions?: string | null;
  valid_from?: string | null;
  valid_to?: string | null;
}

// Create request interface
export interface CreateCouponRequest {
  name: string;
  coupon_type: CouponType;
  coupon_value: number;
  business_type: BusinessType;
  service_type: ServiceType;
  user_id?: number | null;
  coupon_code: string;
  image?: string | null;
  description: string;
  terms_and_conditions?: string | null;
  valid_from?: string | null;
  valid_to?: string | null;
}

// Update request interface (all fields optional)
export interface UpdateCouponRequest {
  name?: string;
  coupon_type?: CouponType;
  coupon_value?: number;
  business_type?: BusinessType;
  service_type?: ServiceType;
  user_id?: number | null;
  coupon_code?: string;
  image?: string | null;
  description?: string;
  terms_and_conditions?: string | null;
  valid_from?: string | null;
  valid_to?: string | null;
}

// Filters interface for list operations
export interface CouponFilters {
  search: string;
  coupon_type?: CouponType | '';
  business_type?: BusinessType | '';
  service_type?: ServiceType | '';
  status?: CouponStatus | '';
  valid_from?: string;
  valid_to?: string;
}

// API Response interface for list operations
export interface CouponListResponse {
  items: Coupon[];
  pagination: {
    current_page: number;
    total_pages: number;
    page_size: number;
    total_items: number;
    has_next: boolean;
    has_previous: boolean;
  };
}

// Statistics interface
export interface CouponStats {
  totalCoupons: number;
  activeCoupons: number;
  expiredCoupons: number;
  draftCoupons: number;
  lastUpdated: string;
}

// Default filter values
export const getDefaultCouponFilters = (): CouponFilters => ({
  search: '',
  coupon_type: '',
  business_type: '',
  service_type: '',
  status: '',
  valid_from: '',
  valid_to: ''
});

// Coupon type options for dropdowns
export const COUPON_TYPE_OPTIONS = [
  { value: 'PERCENTAGE', label: 'Percentage Discount' },
  { value: 'FIXED', label: 'Fixed Amount' },

] as const;

// Business type options for dropdowns
export const BUSINESS_TYPE_OPTIONS = [
  { value: 'B2B', label: 'Business to Business' },
  { value: 'B2C', label: 'Business to Consumer' },
  { value: 'BOTH', label: 'Both B2B & B2C' }
] as const;

// Service type options for dropdowns
export const SERVICE_TYPE_OPTIONS = [
  { value: 'HOTEL', label: 'Hotel Bookings' },
  { value: 'FLIGHT', label: 'Flight Bookings' },
  { value: 'PACKAGE', label: 'Travel Packages' },
  { value: 'ALL', label: 'All Services' }
] as const;

// Status options for dropdowns
export const COUPON_STATUS_OPTIONS = [
  { value: 'ACTIVE', label: 'Active' },
  { value: 'INACTIVE', label: 'Inactive' },
  { value: 'EXPIRED', label: 'Expired' },
  { value: 'DRAFT', label: 'Draft' }
] as const;

// Utility functions
export const CouponUtils = {
  // Format coupon value based on type
  formatCouponValue: (value: number, type: CouponType): string => {
    switch (type) {
      case 'PERCENTAGE':
        return `${value}%`;
      case 'FIXED':
        return `$${value.toFixed(2)}`;
 
      default:
        return value.toString();
    }
  },

  // Get coupon status based on dates
  getCouponStatus: (coupon: Coupon): CouponStatus => {
    const now = new Date();
    const validFrom = coupon.valid_from ? new Date(coupon.valid_from) : null;
    const validTo = coupon.valid_to ? new Date(coupon.valid_to) : null;

    if (validTo && validTo < now) {
      return 'EXPIRED';
    }
    if (validFrom && validFrom > now) {
      return 'DRAFT';
    }
    return 'ACTIVE';
  },

  // Check if coupon is currently valid
  isValidNow: (coupon: Coupon): boolean => {
    const now = new Date();
    const validFrom = coupon.valid_from ? new Date(coupon.valid_from) : null;
    const validTo = coupon.valid_to ? new Date(coupon.valid_to) : null;

    if (validFrom && validFrom > now) return false;
    if (validTo && validTo < now) return false;
    return true;
  },

  // Format date for display
  formatDate: (dateString: string | null): string => {
    if (!dateString) return 'No expiry';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  },

  // Generate random coupon code
  generateCouponCode: (prefix: string = ''): string => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = prefix;
    for (let i = 0; i < (8 - prefix.length); i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  },

  // Validate coupon code format
  isValidCouponCode: (code: string): boolean => {
    return /^[A-Z0-9]{4,12}$/.test(code);
  },

  // Get type label
  getTypeLabel: (type: CouponType): string => {
    const option = COUPON_TYPE_OPTIONS.find(opt => opt.value === type);
    return option?.label || type;
  },

  // Get business type label
  getBusinessTypeLabel: (type: BusinessType): string => {
    const option = BUSINESS_TYPE_OPTIONS.find(opt => opt.value === type);
    return option?.label || type;
  },

  // Get service type label
  getServiceTypeLabel: (type: ServiceType): string => {
    const option = SERVICE_TYPE_OPTIONS.find(opt => opt.value === type);
    return option?.label || type;
  }
};

export default Coupon;
