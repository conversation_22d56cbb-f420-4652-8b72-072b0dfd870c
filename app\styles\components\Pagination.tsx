'use client';

import { useState } from 'react';

interface PaginationProps {
  currentPage: number;
  totalPages: number;
  itemsPerPage: number;
  totalItems: number;
  onPageChange: (page: number) => void;
  onItemsPerPageChange: (itemsPerPage: number) => void;
  itemsPerPageOptions?: number[];
  showItemsPerPage?: boolean;
  // Type selection props
  selectedType?: string;
  onTypeChange?: (type: string) => void;
  typeOptions?: { value: string; label: string }[];
  showTypeFilter?: boolean;
}

export default function Pagination({
  currentPage,
  totalPages,
  itemsPerPage,
  totalItems,
  onPageChange,
  onItemsPerPageChange,
  itemsPerPageOptions = [5, 10, 20, 50],
  showItemsPerPage = true,
  selectedType = '',
  onTypeChange,
  typeOptions = [],
  showTypeFilter = false
}: PaginationProps) {
  const [tempItemsPerPage, setTempItemsPerPage] = useState(itemsPerPage);
  const [tempSelectedType, setTempSelectedType] = useState(selectedType);
  const [showCustomInput, setShowCustomInput] = useState(false);
  const [customValue, setCustomValue] = useState('');

  const handleSelectChange = (value: string) => {
    if (value === 'custom') {
      setShowCustomInput(true);
      setCustomValue('');
    } else {
      setShowCustomInput(false);
      setTempItemsPerPage(Number(value));
    }
  };

  const handleCustomInputChange = (value: string) => {
    setCustomValue(value);
    const numValue = Number(value);
    if (numValue > 0) {
      setTempItemsPerPage(numValue);
    }
  };

  const handleApply = () => {
    if (tempItemsPerPage !== itemsPerPage) {
      onItemsPerPageChange(tempItemsPerPage);
    }
    if (tempSelectedType !== selectedType && onTypeChange) {
      onTypeChange(tempSelectedType);
    }
    setShowCustomInput(false);
    setCustomValue('');
  };

  const handlePrevious = () => {
    if (currentPage > 1) {
      onPageChange(currentPage - 1);
    }
  };

  const handleNext = () => {
    if (currentPage < totalPages) {
      onPageChange(currentPage + 1);
    }
  };

  const getVisiblePages = () => {
    const delta = 2;
    const range = [];
    const rangeWithDots = [];

    for (let i = Math.max(2, currentPage - delta); i <= Math.min(totalPages - 1, currentPage + delta); i++) {
      range.push(i);
    }

    if (currentPage - delta > 2) {
      rangeWithDots.push(1, '...');
    } else {
      rangeWithDots.push(1);
    }

    rangeWithDots.push(...range);

    if (currentPage + delta < totalPages - 1) {
      rangeWithDots.push('...', totalPages);
    } else if (totalPages > 1) {
      rangeWithDots.push(totalPages);
    }

    return rangeWithDots;
  };

  const visiblePages = getVisiblePages();

  return (
    <div className="flex items-center justify-between bg-gray-800 text-white px-4 py-3 rounded-lg">
      {/* Left side - Items per page and Type filter */}
      <div className="flex items-center space-x-6">
        {showItemsPerPage && (
          <div className="flex items-center space-x-3">
            <span className="text-sm font-medium text-gray-300">Result per page:</span>
            <div className="flex items-center space-x-2">
              <select
                value={showCustomInput ? 'custom' : tempItemsPerPage}
                onChange={(e) => handleSelectChange(e.target.value)}
                className="bg-gray-700 border border-gray-600 rounded px-2 py-1 text-sm text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {itemsPerPageOptions.map((option) => (
                  <option key={option} value={option}>
                    {option}
                  </option>
                ))}
                <option value="custom">Custom</option>
              </select>
              {showCustomInput && (
                <input
                  type="number"
                  value={customValue}
                  onChange={(e) => handleCustomInputChange(e.target.value)}
                  placeholder="Enter number"
                  min="1"
                  max="1000"
                  className="bg-gray-700 border border-gray-600 rounded px-2 py-1 text-sm text-white w-24 focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              )}
            </div>
          </div>
        )}

        {showTypeFilter && typeOptions.length > 0 && (
          <div className="flex items-center space-x-3">
            <span className="text-sm font-medium text-gray-300">Type:</span>
            <select
              value={tempSelectedType}
              onChange={(e) => setTempSelectedType(e.target.value)}
              className="bg-gray-700 border border-gray-600 rounded px-2 py-1 text-sm text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">All Types</option>
              {typeOptions.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>
        )}

        {(showItemsPerPage || (showTypeFilter && typeOptions.length > 0)) && (
          <button
            onClick={handleApply}
            className="bg-blue-600 hover:bg-blue-700 px-3 py-1 rounded text-sm font-medium transition-colors"
          >
            Apply
          </button>
        )}
      </div>

      {/* Right side - Page navigation */}
      <div className="flex items-center space-x-2">
        {/* Previous button */}
        <button
          onClick={handlePrevious}
          disabled={currentPage === 1}
          className={`px-3 py-1 rounded text-sm font-medium transition-colors ${
            currentPage === 1
              ? 'text-gray-500 cursor-not-allowed'
              : 'text-gray-300 hover:text-white hover:bg-gray-700'
          }`}
        >
          <i className="ri-arrow-left-s-line mr-1"></i>
          Previous
        </button>

        {/* Page numbers */}
        <div className="flex items-center space-x-1">
          {visiblePages.map((page, index) => (
            <div key={index}>
              {page === '...' ? (
                <span className="px-2 py-1 text-gray-400">...</span>
              ) : (
                <button
                  onClick={() => onPageChange(page as number)}
                  className={`w-8 h-8 rounded text-sm font-medium transition-colors ${
                    currentPage === page
                      ? 'bg-blue-600 text-white'
                      : 'text-gray-300 hover:text-white hover:bg-gray-700'
                  }`}
                >
                  {page}
                </button>
              )}
            </div>
          ))}
        </div>

        {/* Next button */}
        <button
          onClick={handleNext}
          disabled={currentPage === totalPages}
          className={`px-3 py-1 rounded text-sm font-medium transition-colors ${
            currentPage === totalPages
              ? 'text-gray-500 cursor-not-allowed'
              : 'text-gray-300 hover:text-white hover:bg-gray-700'
          }`}
        >
          Next
          <i className="ri-arrow-right-s-line ml-1"></i>
        </button>
      </div>
    </div>
  );
}
