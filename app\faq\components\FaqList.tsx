'use client';

import React, { useState } from 'react';
import { Faq, formatDate, getServiceOption, getPageTypeOption } from '../faq.model';
import Pagination from '../../styles/components/Pagination';

interface FaqListProps {
  faqs: Faq[];
  onEdit: (faq: Faq) => void;
  onView: (faq: Faq) => void;
  onDelete: (id: number) => void;
  loading?: boolean;
  currentPage: number;
  totalPages: number;
  pageSize: number;
  totalItems: number;
  onPageChange: (page: number) => void;
  onPageSizeChange: (pageSize: number) => void;
}

export default function FaqList({
  faqs = [],
  onEdit,
  onView,
  onDelete,
  loading = false,
  currentPage,
  totalPages,
  pageSize,
  totalItems,
  onPageChange,
  onPageSizeChange
}: FaqListProps) {
  const [sortField, setSortField] = useState<keyof Faq>('ID');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');

  // Handle sorting
  const handleSort = (field: keyof Faq) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  // Sort FAQs
  const sortedFaqs = [...faqs].sort((a, b) => {
    let aValue = a[sortField];
    let bValue = b[sortField];

    // Handle different data types
    if (typeof aValue === 'string' && typeof bValue === 'string') {
      aValue = aValue.toLowerCase();
      bValue = bValue.toLowerCase();
    }

    if (aValue < bValue) {
      return sortDirection === 'asc' ? -1 : 1;
    }
    if (aValue > bValue) {
      return sortDirection === 'asc' ? 1 : -1;
    }
    return 0;
  });

  const truncateText = (text: string, maxLength: number = 100) => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  };

  const getSortIcon = (field: keyof Faq) => {
    if (sortField !== field) {
      return 'ri-expand-up-down-line text-gray-400';
    }
    return sortDirection === 'asc' 
      ? 'ri-arrow-up-line text-blue-600' 
      : 'ri-arrow-down-line text-blue-600';
  };

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
      {/* Header */}
      <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">FAQ List</h3>
            <p className="text-sm text-gray-600">
              {loading ? 'Loading...' : `${totalItems} FAQ${totalItems !== 1 ? 's' : ''} found`}
            </p>
          </div>
        </div>
      </div>

      {/* Table */}
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th 
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('ID')}
              >
                <div className="flex items-center space-x-1">
                  <span>ID</span>
                  <i className={getSortIcon('ID')}></i>
                </div>
              </th>
              <th 
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('question')}
              >
                <div className="flex items-center space-x-1">
                  <span>Question</span>
                  <i className={getSortIcon('question')}></i>
                </div>
              </th>
              <th 
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('service')}
              >
                <div className="flex items-center space-x-1">
                  <span>Service</span>
                  <i className={getSortIcon('service')}></i>
                </div>
              </th>
              <th 
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('page_type')}
              >
                <div className="flex items-center space-x-1">
                  <span>Page Type</span>
                  <i className={getSortIcon('page_type')}></i>
                </div>
              </th>
              <th 
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('language')}
              >
                <div className="flex items-center space-x-1">
                  <span>Language</span>
                  <i className={getSortIcon('language')}></i>
                </div>
              </th>
              <th 
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                onClick={() => handleSort('UpdatedAt')}
              >
                <div className="flex items-center space-x-1">
                  <span>Updated</span>
                  <i className={getSortIcon('UpdatedAt')}></i>
                </div>
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {loading ? (
              <tr>
                <td colSpan={7} className="px-6 py-12 text-center">
                  <div className="flex flex-col items-center justify-center text-gray-500">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mb-4"></div>
                    <p className="text-sm">Loading FAQs...</p>
                  </div>
                </td>
              </tr>
            ) : sortedFaqs.length === 0 ? (
              <tr>
                <td colSpan={7} className="px-6 py-12 text-center">
                  <div className="flex flex-col items-center justify-center text-gray-500">
                    <i className="ri-question-line text-4xl mb-4 block"></i>
                    <p className="text-lg font-medium">No FAQs found</p>
                    <p className="text-sm">Try adjusting your search filters</p>
                  </div>
                </td>
              </tr>
            ) : (
              sortedFaqs.map((faq) => (
                <tr key={faq.ID} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">#{faq.ID}</div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-900 font-medium">
                      {truncateText(faq.question, 80)}
                    </div>
                    <div className="text-sm text-gray-500 mt-1">
                      {truncateText(faq.answer, 60)}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                      {getServiceOption(faq.service)?.label || faq.service}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                      {getPageTypeOption(faq.page_type)?.label || faq.page_type}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-8 w-8">
                        <div className="h-8 w-8 rounded-lg bg-gradient-to-br from-purple-100 to-purple-200 flex items-center justify-center">
                          <span className="text-purple-700 text-xs font-medium">
                            {faq.language_code.toUpperCase()}
                          </span>
                        </div>
                      </div>
                      <div className="ml-3">
                        <div className="text-sm font-medium text-gray-900">{faq.language}</div>
                        <div className="text-sm text-gray-500">{faq.language_code}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {formatDate(faq.UpdatedAt)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex items-center justify-end space-x-2">
                      <button
                        onClick={() => onView(faq)}
                        className="text-blue-600 hover:text-blue-900 p-1 rounded hover:bg-blue-50"
                        title="View Details"
                      >
                        <i className="ri-eye-line"></i>
                      </button>
                      <button
                        onClick={() => onEdit(faq)}
                        className="text-green-600 hover:text-green-900 p-1 rounded hover:bg-green-50"
                        title="Edit FAQ"
                      >
                        <i className="ri-edit-line"></i>
                      </button>
                      <button
                        onClick={() => onDelete(faq.ID)}
                        className="text-red-600 hover:text-red-900 p-1 rounded hover:bg-red-50"
                        title="Delete FAQ"
                      >
                        <i className="ri-delete-bin-line"></i>
                      </button>
                    </div>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {faqs && faqs.length > 0 && (
        <div className="mt-6 px-6 pb-6">
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            itemsPerPage={pageSize}
            totalItems={totalItems}
            onPageChange={onPageChange}
            onItemsPerPageChange={onPageSizeChange}
          />
        </div>
      )}
    </div>
  );
}
