import apiService from '../api/api-service';
import { 
  Faq, 
  CreateFaqRequest, 
  UpdateFaqRequest, 
  FaqFilters,
  SingleFaqResponse,
  MultipleFaqResponse,
  FaqByLanguageResponse,
  FaqStats
} from './faq.model';

const BASE_URL = '/faqs';

// Mock data for development/fallback
const getMockFaqs = (): Faq[] => [
  // English FAQs
  {
    ID: 1,
    CreatedAt: "2025-10-10T10:48:20.56597+05:30",
    UpdatedAt: "2025-10-10T10:48:20.56597+05:30",
    DeletedAt: null,
    question: "Is breakfast included in the booking?",
    answer: "Yes, complimentary breakfast is included for all guests.",
    service: "hotel",
    page_type: "booking",
    service_id: 1,
    language: "English",
    language_code: "en"
  },

];

// Get all FAQs
export const getAllFaqs = async (): Promise<Faq[]> => {
  try {
    const response = await apiService.getfaq<MultipleFaqResponse>(BASE_URL);
    if (response.success) {
      return response.data;
    }
    throw new Error(response.message || 'Failed to fetch FAQs');
  } catch (error) {
    console.warn('API not available, using mock data:', error);

    // Return mock data as fallback
    return getMockFaqs();
  }
};

// Get FAQ by ID
export const getFaqById = async (id: number): Promise<Faq> => {
  try {
    const response = await apiService.getfaq<SingleFaqResponse>(`${BASE_URL}/${id}`);
    if (response.success) {
      return response.data;
    }
    throw new Error(response.message || 'Failed to fetch FAQ');
  } catch (error) {
    console.warn('API not available, using mock data:', error);

    // Return mock data as fallback
    const mockData = getMockFaqs();
    const found = mockData.find(item => item.ID === id);
    if (found) {
      return found;
    }
    throw new Error(`FAQ not found with ID: ${id}`);
  }
};

// Get FAQs by language code
export const getFaqsByLanguage = async (languageCode: string): Promise<Faq[]> => {
  try {
    const response = await apiService.getfaq<FaqByLanguageResponse>(`${BASE_URL}/language/${languageCode}`);
    if (response.success) {
      return response.data.data;
    }
    throw new Error(response.message || 'Failed to fetch FAQs by language');
  } catch (error) {
    console.warn('API not available, using mock data:', error);

    // Return mock data as fallback
    const mockData = getMockFaqs();
    return mockData.filter(item => item.language_code === languageCode);
  }
};

// Create new FAQ
export const createFaq = async (data: CreateFaqRequest): Promise<Faq> => {
  try {
    const response = await apiService.postfaq<SingleFaqResponse>(BASE_URL, data);
    if (response.success) {
      return response.data;
    }
    throw new Error(response.message || 'Failed to create FAQ');
  } catch (error) {
    console.warn('API not available, simulating FAQ creation:', error);

    // Simulate creating a new FAQ with mock data
    const newFaq: Faq = {
      ID: Math.max(...getMockFaqs().map(f => f.ID)) + 1,
      CreatedAt: new Date().toISOString(),
      UpdatedAt: new Date().toISOString(),
      DeletedAt: null,
      ...data
    };

    return newFaq;
  }
};

// Update FAQ
export const updateFaq = async (id: number, data: UpdateFaqRequest): Promise<Faq> => {
  try {
    const response = await apiService.putfaq<SingleFaqResponse>(`${BASE_URL}/${id}`, data);
    if (response.success) {
      return response.data;
    }
    throw new Error(response.message || 'Failed to update FAQ');
  } catch (error) {
    console.warn('API not available, simulating FAQ update:', error);

    // Simulate updating FAQ with mock data
    const mockData = getMockFaqs();
    const existingFaq = mockData.find(f => f.ID === id);
    if (existingFaq) {
      return {
        ...existingFaq,
        ...data,
        UpdatedAt: new Date().toISOString()
      };
    }
    throw new Error(`FAQ not found with ID: ${id}`);
  }
};

// Delete FAQ
export const deleteFaq = async (id: number): Promise<void> => {
  try {
    const response = await apiService.deletefaq<{ success: boolean; message?: string }>(`${BASE_URL}/${id}`);
    if (!response.success) {
      throw new Error(response.message || 'Failed to delete FAQ');
    }
  } catch (error) {
    console.warn('API not available, simulating FAQ deletion:', error);
    // In a real implementation, this would remove from local state
    // For now, we just log the deletion
  }
};

// Search FAQs with filters
export const searchFaqs = async (filters: FaqFilters): Promise<Faq[]> => {
  try {
    const params = new URLSearchParams();
    
    if (filters.search) params.append('search', filters.search);
    if (filters.language) params.append('language', filters.language);
    if (filters.language_code) params.append('language_code', filters.language_code);
    if (filters.service) params.append('service', filters.service);
    if (filters.page_type) params.append('page_type', filters.page_type);
    if (filters.dateRange.startDate) params.append('start_date', filters.dateRange.startDate);
    if (filters.dateRange.endDate) params.append('end_date', filters.dateRange.endDate);

    const response = await apiService.getfaq<MultipleFaqResponse>(`${BASE_URL}/search?${params.toString()}`);
    if (response.success) {
      return response.data;
    }
    throw new Error(response.message || 'Failed to search FAQs');
  } catch (error) {
    console.warn('API not available, using mock data for search:', error);

    // Return filtered mock data as fallback
    const mockData = getMockFaqs();
    return mockData.filter(item => {
      const matchesSearch = !filters.search ||
        item.question.toLowerCase().includes(filters.search.toLowerCase()) ||
        item.answer.toLowerCase().includes(filters.search.toLowerCase());
      
      const matchesLanguage = !filters.language || item.language === filters.language;
      const matchesLanguageCode = !filters.language_code || item.language_code === filters.language_code;
      const matchesService = !filters.service || item.service === filters.service;
      const matchesPageType = !filters.page_type || item.page_type === filters.page_type;
      
      return matchesSearch && matchesLanguage && matchesLanguageCode && matchesService && matchesPageType;
    });
  }
};

// Get FAQ statistics
export const getFaqStats = async (): Promise<FaqStats> => {
  try {
    const faqs = await getAllFaqs();
    
    const totalFaqs = faqs.length;
    const uniqueLanguages = new Set(faqs.map(faq => faq.language_code)).size;
    const uniqueServices = new Set(faqs.map(faq => faq.service)).size;
    const lastUpdated = faqs.reduce((latest, faq) => {
      return new Date(faq.UpdatedAt) > new Date(latest) ? faq.UpdatedAt : latest;
    }, faqs[0]?.UpdatedAt || new Date().toISOString());

    return {
      totalFaqs,
      totalLanguages: uniqueLanguages,
      totalServices: uniqueServices,
      lastUpdated
    };
  } catch (error) {
    console.warn('API not available, using mock data for stats:', error);

    // Return default stats as fallback
    return {
      totalFaqs: 0,
      totalLanguages: 0,
      totalServices: 0,
      lastUpdated: new Date().toISOString()
    };
  }
};

const FaqService = {
  getAllFaqs,
  getFaqById,
  getFaqsByLanguage,
  createFaq,
  updateFaq,
  deleteFaq,
  searchFaqs,
  getFaqStats
};

export default FaqService;
