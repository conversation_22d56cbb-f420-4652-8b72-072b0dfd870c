'use client';

import { Offer, OfferUtils } from '../offer.model';

interface OfferViewProps {
  offer: Offer | null;
  onEdit?: (offer: Offer) => void;
  onClose: () => void;
}

export default function OfferView({ offer, onEdit, onClose }: OfferViewProps) {
  if (!offer) {
    return (
      <div className="p-6 text-center">
        <p className="text-gray-500">No offer data available</p>
      </div>
    );
  }

  const status = OfferUtils.getOfferStatus(offer);
  const isValid = OfferUtils.isValidNow(offer);

  const getStatusConfig = () => {
    const configs = {
      ACTIVE: { bg: 'bg-green-100', text: 'text-green-800', icon: 'ri-check-line' },
      INACTIVE: { bg: 'bg-gray-100', text: 'text-gray-800', icon: 'ri-pause-line' },
      EXPIRED: { bg: 'bg-red-100', text: 'text-red-800', icon: 'ri-time-line' },
      DRAFT: { bg: 'bg-yellow-100', text: 'text-yellow-800', icon: 'ri-draft-line' }
    };
    return configs[status];
  };

  const statusConfig = getStatusConfig();

  return (
    <div className="space-y-6">
      {/* Offer Header */}
      <div className="flex items-center space-x-4 p-6 bg-gradient-to-r from-orange-50 to-red-50 rounded-lg border border-orange-200">
        <div className="w-16 h-16 bg-gradient-to-br from-orange-100 to-orange-200 rounded-xl flex items-center justify-center">
          {offer.image ? (
            <img 
              src={offer.image} 
              alt={offer.name}
              className="w-16 h-16 rounded-xl object-cover"
            />
          ) : (
            <i className="ri-gift-line text-orange-700 text-2xl"></i>
          )}
        </div>
        <div className="flex-1">
          <h2 className="text-2xl font-bold text-gray-900">{offer.name}</h2>
          <p className="text-lg text-gray-600 font-mono">{offer.offer_code}</p>
          <div className="flex items-center space-x-4 mt-2">
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusConfig.bg} ${statusConfig.text}`}>
              <i className={`${statusConfig.icon} mr-1`}></i>
              {status}
            </span>
            {isValid && (
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                <i className="ri-check-double-line mr-1"></i>
                Currently Valid
              </span>
            )}
          </div>
        </div>
        {onEdit && (
          <button
            onClick={() => onEdit(offer)}
            className="px-4 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 text-sm font-medium text-gray-700"
          >
            <i className="ri-edit-line mr-2"></i>
            Edit
          </button>
        )}
      </div>

      {/* Offer Details */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Basic Information */}
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Basic Information</h3>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-500">Offer Type</label>
              <p className="text-lg font-semibold text-gray-900">{OfferUtils.getTypeLabel(offer.offer_type)}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-500">Discount Value</label>
              <p className="text-2xl font-bold text-orange-600">
                {OfferUtils.formatOfferValue(offer.offer_value, offer.offer_type)}
              </p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-500">Business Type</label>
              <p className="text-lg font-semibold text-gray-900">{OfferUtils.getBusinessTypeLabel(offer.business_type)}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-500">Service Type</label>
              <p className="text-lg font-semibold text-gray-900">{OfferUtils.getServiceTypeLabel(offer.service_type)}</p>
            </div>
          </div>
        </div>

        {/* Validity Information */}
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Validity Information</h3>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-500">Valid From</label>
              <p className="text-lg font-semibold text-gray-900">
                {offer.valid_from ? OfferUtils.formatDate(offer.valid_from) : 'No start date'}
              </p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-500">Valid To</label>
              <p className="text-lg font-semibold text-gray-900">
                {OfferUtils.formatDate(offer.valid_to)}
              </p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-500">Current Status</label>
              <div className="flex items-center space-x-2">
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusConfig.bg} ${statusConfig.text}`}>
                  <i className={`${statusConfig.icon} mr-1`}></i>
                  {status}
                </span>
                {isValid ? (
                  <span className="text-sm text-green-600">✓ Active and usable</span>
                ) : (
                  <span className="text-sm text-red-600">✗ Not currently usable</span>
                )}
              </div>
            </div>
            {offer.user_id && (
              <div>
                <label className="block text-sm font-medium text-gray-500">User Specific</label>
                <p className="text-lg font-semibold text-gray-900">User ID: {offer.user_id}</p>
                <p className="text-sm text-blue-600">This offer is restricted to a specific user</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Description and Terms */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Description & Terms</h3>
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-500 mb-2">Description</label>
            <p className="text-gray-900 leading-relaxed">{offer.description}</p>
          </div>
          {offer.terms_and_conditions && (
            <div>
              <label className="block text-sm font-medium text-gray-500 mb-2">Terms and Conditions</label>
              <div className="bg-gray-50 rounded-lg p-4">
                <p className="text-gray-700 leading-relaxed whitespace-pre-wrap">{offer.terms_and_conditions}</p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Metadata */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Metadata</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-3">
            <div>
              <label className="block text-sm font-medium text-gray-500">Created</label>
              <p className="text-sm text-gray-900">{OfferUtils.formatDate(offer.created_at)}</p>
              <p className="text-xs text-gray-500">by {offer.created_by}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-500">Last Updated</label>
              <p className="text-sm text-gray-900">{OfferUtils.formatDate(offer.updated_at)}</p>
              <p className="text-xs text-gray-500">by {offer.updated_by}</p>
            </div>
          </div>
          <div className="space-y-3">
            <div>
              <label className="block text-sm font-medium text-gray-500">Offer ID</label>
              <p className="text-sm text-gray-900 font-mono">#{offer.id}</p>
            </div>
            {offer.deleted_at && (
              <div>
                <label className="block text-sm font-medium text-gray-500">Deleted</label>
                <p className="text-sm text-red-600">{OfferUtils.formatDate(offer.deleted_at)}</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Usage Information (if available) */}
      {(offer.flights || offer.hotels) && (
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Usage Restrictions</h3>
          <div className="space-y-4">
            {offer.flights && (
              <div>
                <label className="block text-sm font-medium text-gray-500 mb-2">Flight Restrictions</label>
                <div className="bg-blue-50 rounded-lg p-3">
                  <pre className="text-sm text-blue-800 whitespace-pre-wrap">{JSON.stringify(offer.flights, null, 2)}</pre>
                </div>
              </div>
            )}
            {offer.hotels && (
              <div>
                <label className="block text-sm font-medium text-gray-500 mb-2">Hotel Restrictions</label>
                <div className="bg-green-50 rounded-lg p-3">
                  <pre className="text-sm text-green-800 whitespace-pre-wrap">{JSON.stringify(offer.hotels, null, 2)}</pre>
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
        <button
          onClick={onClose}
          className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
        >
          Close
        </button>
        {onEdit && (
          <button
            onClick={() => onEdit(offer)}
            className="px-4 py-2 text-sm font-medium text-white bg-orange-600 border border-transparent rounded-lg hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
          >
            <i className="ri-edit-line mr-2"></i>
            Edit Offer
          </button>
        )}
      </div>
    </div>
  );
}
