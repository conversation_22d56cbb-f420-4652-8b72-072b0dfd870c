// 'use client';

// import { useRef } from 'react';
// import VirtualSearchForm from './components/VirtualSearchForm';

// export default function VirtualPage() {
//   const formRef = useRef<{ resetForm: () => void }>(null);
//   return (
//     <div className="h-full flex flex-col">
//       {/* Enhanced Professional Header */}
//       <div className="flex-shrink-0 bg-white border-b border-gray-200">
//         <div className="px-6 py-6">
//           <div className="flex flex-col space-y-4 lg:flex-row lg:items-center lg:justify-between lg:space-y-0">
//             <div className="flex-1 min-w-0">
//               <div className="flex items-center space-x-3 mb-2">
//                 <div className="w-10 h-10 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-xl flex items-center justify-center">
//                   <i className="ri-search-line text-white text-lg"></i>
//                 </div>
//                 <div>
//                   <h1 className="text-2xl font-bold text-gray-900">
//                     Virtual Search
//                   </h1>
//                   <p className="text-sm text-gray-600">
//                     Advanced search functionality with dynamic filtering and conditional logic
//                   </p>
//                 </div>
//               </div>
              
//               {/* Quick Stats */}
//               <div className="flex items-center space-x-6 text-sm text-gray-500">
//                 <div className="flex items-center space-x-1">
//                   <i className="ri-filter-line"></i>
//                   <span>Dynamic Filters</span>
//                 </div>
//                 <div className="flex items-center space-x-1">
//                   <i className="ri-function-line"></i>
//                   <span>Function-based Search</span>
//                 </div>
//                 <div className="flex items-center space-x-1">
//                   <i className="ri-database-line"></i>
//                   <span>Multi-Model Support</span>
//                 </div>
//                 <div className="flex items-center space-x-1">
//                   <i className="ri-code-line"></i>
//                   <span>JSON Output</span>
//                 </div>
//               </div>
//             </div>
            
//             {/* Header Actions */}
//             <div className="flex items-center space-x-3">
//               <div className="bg-indigo-50 px-4 py-2 rounded-lg">
//                 <div className="flex items-center space-x-2">
//                   <i className="ri-search-2-line text-indigo-600"></i>
//                   <span className="text-sm font-medium text-indigo-700">
//                     Search & Filter
//                   </span>
//                 </div>
//               </div>
              
//               {/* Quick Actions */}
//               <div className="flex items-center space-x-2">
//                 <button className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors">
//                   <i className="ri-download-line" title="Export Results"></i>
//                 </button>
//                 <button
//                   onClick={() => formRef.current?.resetForm()}
//                   className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
//                 >
//                   <i className="ri-refresh-line" title="Reset Form"></i>
//                 </button>
//                 <button className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors">
//                   <i className="ri-settings-3-line" title="Settings"></i>
//                 </button>
//               </div>
//             </div>
//           </div>
//         </div>
//       </div>

//       {/* Main Content Area */}
//       <div className="flex-1 overflow-y-auto bg-gray-50">
//         <div className="p-6">
//           <div className="max-w-full">
//             <VirtualSearchForm ref={formRef} />
//           </div>
//         </div>
//       </div>
//     </div>
//   );
// }


// 'use client';

// import { useRef, useState, useEffect } from 'react';
// import VirtualSearchForm, { VirtualSearchFormRef } from './components/VirtualSearchForm'; // Adjust path
// import VirtualList from './components/VirtualList'; // Adjust path
// import virtualService from './virtual.service'; // Adjust path
// import { Virtual, VirtualFilters } from './virtual.model'; // Adjust path

// export default function VirtualPage() {
//   const formRef = useRef<VirtualSearchFormRef>(null);

//   // State for the list
//   const [virtuals, setVirtuals] = useState<Virtual[]>([]);
//   const [loading, setLoading] = useState(true);
  
//   // State for pagination
//   const [currentPage, setCurrentPage] = useState(1);
//   const [totalPages, setTotalPages] = useState(1);
//   const [pageSize, setPageSize] = useState(10);
//   const [totalItems, setTotalItems] = useState(0);

//   // State for filtering
//   const [filters, setFilters] = useState<VirtualFilters>({ search: '' });

//   // State for editing
//   const [editingVirtual, setEditingVirtual] = useState<Virtual | null>(null);

//   // --- Data Fetching Logic ---
//   const loadVirtuals = async () => {
//     setLoading(true);
//     try {
//       const response = await virtualService.getVirtuals(currentPage, pageSize, filters);
//       setVirtuals(response.data);
//       setTotalItems(response.total);
//       setTotalPages(response.totalPages);
//       setCurrentPage(response.page);
//     } catch (error) {
//       console.error("Failed to load virtuals:", error);
//       alert("Failed to load virtual searches.");
//     } finally {
//       setLoading(false);
//     }
//   };

//   // Reload data when page, size, or filters change
//   useEffect(() => {
//     loadVirtuals();
//   }, [currentPage, pageSize, filters]);

//   // --- Handlers for List ---
//   const handlePageChange = (page: number) => {
//     setCurrentPage(page);
//   };

//   const handlePageSizeChange = (size: number) => {
//     setPageSize(size);
//     setCurrentPage(1); // Reset to page 1
//   };

//   const handleDelete = async (id: number) => {
//     if (confirm('Are you sure you want to delete this search?')) {
//       try {
//         await virtualService.deleteVirtual(id);
//         alert('Search deleted successfully.');
//         // If it was the last item on the page, go back one page
//         if (virtuals.length === 1 && currentPage > 1) {
//           setCurrentPage(prev => prev - 1);
//         } else {
//           loadVirtuals(); // Reload current page
//         }
//       } catch (error) {
//         console.error("Failed to delete virtual:", error);
//         alert("Failed to delete search.");
//       }
//     }
//   };
  
//   const handleEdit = (virtual: Virtual) => {
//     setEditingVirtual(virtual);
//     // Scroll to the top to show the form
//     window.scrollTo({ top: 0, behavior: 'smooth' });
//   };
  
//   const handleView = (virtual: Virtual) => {
//     // Implement a modal view later
//     alert(`Viewing Virtual #${virtual.id}:\n${virtual.name}\n\n${JSON.stringify(virtual, null, 2)}`);
//   };

//   // --- Handlers for Form & Page Header ---
//   const handleFormSave = (savedVirtual: Virtual) => {
//     setEditingVirtual(null); // Clear editing state
//     // Reset to page 1 to see the new/updated item
//     if (currentPage !== 1) {
//       setCurrentPage(1);
//     } else {
//       loadVirtuals(); // Reload list
//     }
//   };

//   const handleFormReset = () => {
//     formRef.current?.resetForm();
//     setEditingVirtual(null);
//   };

//   const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
//     // This will trigger a reload via useEffect
//     // In a real app, you might want to debounce this
//     setFilters(prev => ({ ...prev, search: e.target.value }));
//     setCurrentPage(1); // Reset to page 1 on search
//   };

//   return (
//     <div className="h-full flex flex-col">
//       {/* Enhanced Professional Header */}
//       <div className="flex-shrink-0 bg-white border-b border-gray-200">
//         <div className="px-6 py-6">
//           <div className="flex flex-col space-y-4 lg:flex-row lg:items-center lg:justify-between lg:space-y-0">
//             <div className="flex-1 min-w-0">
//               <div className="flex items-center space-x-3 mb-2">
//                 <div className="w-10 h-10 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-xl flex items-center justify-center">
//                   <i className="ri-search-line text-white text-lg"></i>
//                 </div>
//                 <div>
//                   <h1 className="text-2xl font-bold text-gray-900">
//                     Virtual Search
//                   </h1>
//                   <p className="text-sm text-gray-600">
//                     Advanced search functionality with dynamic filtering and conditional logic
//                   </p>
//                 </div>
//               </div>
//               {/* Quick Stats */}
//               <div className="flex items-center space-x-6 text-sm text-gray-500">
//                 <div className="flex items-center space-x-1"><i className="ri-filter-line"></i><span>Dynamic Filters</span></div>
//                 <div className="flex items-center space-x-1"><i className="ri-function-line"></i><span>Function-based Search</span></div>
//                 <div className="flex items-center space-x-1"><i className="ri-database-line"></i><span>Multi-Model Support</span></div>
//               </div>
//             </div>
//             {/* Header Actions */}
//             <div className="flex items-center space-x-3">
//               <div className="bg-indigo-50 px-4 py-2 rounded-lg">
//                 <div className="flex items-center space-x-2">
//                   <i className="ri-search-2-line text-indigo-600"></i>
//                   <span className="text-sm font-medium text-indigo-700">Search & Filter</span>
//                 </div>
//               </div>
//               <div className="flex items-center space-x-2">
//                 <button
//                   onClick={handleFormReset} // Hooked up reset button
//                   className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
//                 >
//                   <i className="ri-refresh-line" title="Reset Form"></i>
//                 </button>
//                 {/* Other header buttons... */}
//               </div>
//             </div>
//           </div>
//         </div>
//       </div>

//       {/* Main Content Area */}
//       <div className="flex-1 overflow-y-auto bg-gray-50">
//         <div className="p-6">
//           <div className="max-w-full space-y-6">
            
//             {/* 1. The Form */}
//             <VirtualSearchForm 
//               ref={formRef} 
//               onFormSave={handleFormSave} 
//               editingVirtual={editingVirtual}
//             />

//             {/* 2. Filter Bar for the List */}
//             <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
//               <div className="flex items-center">
//                 <i className="ri-search-line text-gray-400 mr-2"></i>
//                 <input
//                   type="text"
//                   placeholder="Search by name, description, or tag..."
//                   value={filters.search}
//                   onChange={handleSearchChange}
//                   className="w-full p-2 border border-gray-300 rounded-lg"
//                 />
//                 {/* TODO: Add other filters for model, status, etc. here */}
//               </div>
//             </div>

//             {/* 3. The List */}
//             <VirtualList
//               virtuals={virtuals}
//               loading={loading}
//               onDelete={handleDelete}
//               onEdit={handleEdit}
//               onView={handleView}
//               currentPage={currentPage}
//               totalPages={totalPages}
//               pageSize={pageSize}
//               totalItems={totalItems}
//               onPageChange={handlePageChange}
//               onPageSizeChange={handlePageSizeChange}
//             />
//           </div>
//         </div>
//       </div>
//     </div>
//   );
// }


'use client';

import { useRef, useState, useEffect, useCallback } from 'react';
import VirtualSearchForm, { VirtualSearchFormRef } from './components/VirtualSearchForm'; 
import VirtualList from './components/VirtualList';
import { VirtualGroup } from './virtual.model';
import virtualService from './virtual.service';
import ViewVirtualModal from './components/ViewVirtualModal';
import EditVirtualModal from './components/EditVirtualModal';

export default function VirtualPage() {
  const createFormRef = useRef<VirtualSearchFormRef>(null);

  // State for the list data (now VirtualGroup[])
  const [virtuals, setVirtuals] = useState<VirtualGroup[]>([]);
  const [loading, setLoading] = useState(true);
  
  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [totalItems, setTotalItems] = useState(0);
  
  // State for filtering (not yet implemented)
  // const [filters, setFilters] = useState<VirtualFilters>({ search: '' });

  // State for Modals
  const [viewingVirtual, setViewingVirtual] = useState<VirtualGroup | null>(null);
  const [editingVirtualData, setEditingVirtualData] = useState<VirtualGroup | null>(null);


  // --- Data Fetching Logic ---
  const loadVirtuals = useCallback(async () => {
    setLoading(true);
    try {
      // Fetches groups with pagination
      const response = await virtualService.getVirtuals(currentPage, pageSize);
      setVirtuals(response.data);
      setTotalItems(response.total);
      setTotalPages(response.totalPages);
      setCurrentPage(response.page);
    } catch (error) {
      console.error("Failed to load virtuals:", error);
      alert("Failed to load virtual groups.");
    } finally {
      setLoading(false);
    }
  }, [currentPage, pageSize]);

  // Load data on mount and when pagination changes
  useEffect(() => {
    loadVirtuals();
  }, [loadVirtuals]);

  
  // --- Handlers (View, Edit, Delete) ---
  const handleDelete = async (id: number) => {
    if (confirm('Are you sure you want to delete this group? This may also delete children.')) {
      try {
        await virtualService.deleteVirtual(id);
        alert('Group deleted successfully.');
        loadVirtuals(); // Reload list
      } catch (error) {
        console.error("Failed to delete virtual:", error);
        alert("Failed to delete group.");
      }
    }
  };
  
  const handleEdit = (virtual: VirtualGroup) => {
    setEditingVirtualData(virtual);
    setViewingVirtual(null);
  };
  
  const handleView = (virtual: VirtualGroup) => {
    // We fetch the full details (with children) for the view modal
    setLoading(true);
    virtualService.getVirtualById(virtual.id)
      .then(fullData => {
        setViewingVirtual(fullData);
        setEditingVirtualData(null);
      })
      .catch(err => alert("Failed to fetch group details."))
      .finally(() => setLoading(false));
  };

  // --- Handlers for Forms & Modals ---
  
  const handleCreateSave = (newVirtual: VirtualGroup) => {
    createFormRef.current?.resetForm();
    loadVirtuals(); // Reload list
  };

  const handleEditSave = (updatedVirtual: VirtualGroup) => {
    setEditingVirtualData(null); // Close the modal
    loadVirtuals(); // Reload the list
  };

  const handleFormReset = () => {
    createFormRef.current?.resetForm();
  };

  // --- Pagination Handlers ---
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handlePageSizeChange = (size: number) => {
    setPageSize(size);
    setCurrentPage(1); // Reset to page 1 when changing page size
  };

  return (
    <div className="h-full flex flex-col">
      {/* --- Header (Unchanged) --- */}
      <div className="flex-shrink-0 bg-white border-b border-gray-200">
        <div className="px-6 py-6">
           <div className="flex flex-col space-y-4 lg:flex-row lg:items-center lg:justify-between lg:space-y-0">
             <div className="flex-1 min-w-0">
               <div className="flex items-center space-x-3 mb-2">
                 <div className="w-10 h-10 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-xl flex items-center justify-center">
                   <i className="ri-search-line text-white text-lg"></i>
                 </div>
                 <div>
                   <h1 className="text-2xl font-bold text-gray-900">
                     Virtual Search Groups
                   </h1>
                   <p className="text-sm text-gray-600">
                     Manage reusable rule groups for markups and offers
                   </p>
                 </div>
               </div>
             </div>
             <div className="flex items-center space-x-3">
               <div className="flex items-center space-x-2">
                 <button
                   onClick={handleFormReset}
                   className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
                 >
                   <i className="ri-refresh-line" title="Reset Form"></i>
                 </button>
               </div>
             </div>
          </div>
        </div>
      </div>

      {/* --- Main Content Area --- */}
      <div className="flex-1 overflow-y-auto bg-gray-50">
        <div className="p-6">
          <div className="max-w-full space-y-6">
            
            {/* 1. The "Create" Form */}
            <VirtualSearchForm 
              ref={createFormRef} 
              onVirtualCreated={handleCreateSave}
              allGroups={virtuals} // Pass the list for the parent selector
            />
            
            {/* 2. The List */}
            <VirtualList
              virtuals={virtuals}
              loading={loading}
              onDelete={handleDelete}
              onEdit={handleEdit}
              onView={handleView}
              currentPage={currentPage}
              totalPages={totalPages}
              pageSize={pageSize}
              totalItems={totalItems}
              onPageChange={handlePageChange}
              onPageSizeChange={handlePageSizeChange}
            />
          </div>
        </div>
      </div>

      {/* --- Modals --- */}
      
      <ViewVirtualModal
        virtual={viewingVirtual}
        onClose={() => setViewingVirtual(null)}
      />

      <EditVirtualModal
        virtual={editingVirtualData}
        allGroups={virtuals} // Pass list for parent selector
        isOpen={!!editingVirtualData}
        onSave={handleEditSave}
        onClose={() => setEditingVirtualData(null)}
      />
    </div>
  );
}