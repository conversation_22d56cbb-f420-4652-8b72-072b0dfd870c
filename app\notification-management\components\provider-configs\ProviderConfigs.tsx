"use client";

import { useCallback, useEffect, useState } from "react";
import ProviderConfigsList from "./components/ProviderConfigsList";
import ProviderConfigForm from "./components/ProviderConfigForm";
import Modal from "@/app/components/ui/Modal";
import TabbedModal from "@/app/components/ui/TabbedModal";
import { ProviderConfig } from "../../nm.model";
import { getProviderConfigs, getProviderConfigsByID, createProviderConfigs, updateProviderConfigs, deleteProviderConfigs } from "../../nm-service";

export default function ProviderConfigs() {
  const [configs, setConfigs] = useState<ProviderConfig[]>([]);
  const [loading, setLoading] = useState(false);
  const [viewLoading, setViewLoading] = useState(false);
  const [formLoading, setFormLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const [isFormOpen, setIsFormOpen] = useState(false);
  const [selectedConfig, setSelectedConfig] = useState<ProviderConfig | null>(null);
  const [isViewMode, setIsViewMode] = useState(false);

  const fetchConfigs = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const res = await getProviderConfigs();
      setConfigs(res ?? []);
    } catch (err) {
      console.error("Error fetching provider configs:", err);
      setError("Failed to load provider configurations");
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchConfigs();
  }, [fetchConfigs]);

  // Event handlers
  const handleCreateConfig = () => {
    setSelectedConfig(null);
    setIsViewMode(false);
    setIsFormOpen(true);
  };

  const handleEditConfig = (config: ProviderConfig) => {
    setSelectedConfig(config);
    setIsViewMode(false);
    setIsFormOpen(true);
  };

  const handleViewConfig = async (config: ProviderConfig) => {
    try {
      setIsViewMode(true);
      setIsFormOpen(true);
      setSelectedConfig(null); // Clear previous data
      setViewLoading(true);

      // Fetch fresh config data using string UUID
      const freshConfig = await getProviderConfigsByID(config.id);
      setSelectedConfig(freshConfig);

    } catch (error) {
      console.error("Error fetching config details:", error);
      setError("Failed to load configuration details");
      // Fallback to existing config data
      setSelectedConfig(config);
    } finally {
      setViewLoading(false);
    }
  };

  const handleDeleteConfig = async (config: ProviderConfig) => {
    // Show confirmation dialog
    const isConfirmed = window.confirm(
      `Are you sure you want to delete the configuration "${config.display_name}"?\n\nThis action cannot be undone.`
    );

    if (!isConfirmed) return;

    try {
      setFormLoading(true);
      setError(null);

      // Call delete API
      await deleteProviderConfigs(config.id);

      // Refresh the list after successful deletion
      await fetchConfigs();
    } catch (error: any) {
      console.error("Error deleting config:", error);

      // Enhanced error message for delete operations
      let errorMessage = "Failed to delete configuration";
      if (error?.response?.status === 404) {
        errorMessage = "Configuration not found. It may have already been deleted.";
      } else if (error?.response?.status === 403) {
        errorMessage = "You don't have permission to delete this configuration.";
      } else if (error?.response?.status === 409) {
        errorMessage = "Cannot delete configuration. It may be in use by active notifications.";
      } else if (error?.response?.status >= 500) {
        errorMessage = "Server error. Please try again later.";
      } else if (error?.code === 'NETWORK_ERROR') {
        errorMessage = "Network error. Please check your connection.";
      } else {
        errorMessage = error?.response?.data?.message || error?.message || "Failed to delete configuration";
      }

      setError(`Delete failed: ${errorMessage}`);
    } finally {
      setFormLoading(false);
    }
  };

  const handleFormClose = () => {
    setIsFormOpen(false);
    setSelectedConfig(null);
    setIsViewMode(false);
  };

  // Helper function to convert form data to JSON (not FormData)
  const createRequestBody = (configData: any) => {
    // Return the data as a proper JSON object with correct types matching Go backend
    return {
      company_id: parseInt(configData.company_id) || 101,
      channel: configData.channel,
      provider_type: configData.provider_type,
      display_name: configData.display_name,
      config: configData.config || {},
      priority: parseInt(configData.priority) || 1,
      is_active: Boolean(configData.is_active),
      retry_limit: parseInt(configData.retry_limit) || 3,
      rate_limit: parseInt(configData.rate_limit) || 100,
      retry_count: parseInt(configData.retry_count) || 0,
      retry_delay: parseInt(configData.retry_delay) || 5000,
      timeout_seconds: parseInt(configData.timeout_seconds) || 30,
      circuit_breaker_threshold: parseInt(configData.circuit_breaker_threshold) || 5,
      circuit_breaker_timeout: parseInt(configData.circuit_breaker_timeout) || 60,
      health_check_url: configData.health_check_url,
      health_check_interval: parseInt(configData.health_check_interval) || 300,
      last_health_check: configData.last_health_check || new Date().toISOString(),
      health_status: configData.health_status || 'unknown',
      cost_per_message: parseFloat(configData.cost_per_message) || 0.001,
      monthly_quota: parseInt(configData.monthly_quota) || 10000,
      current_usage: parseInt(configData.current_usage) || 0,
      usage_reset_date: configData.usage_reset_date || new Date().toISOString().split('T')[0],
      tags: configData.tags || {},
      metadata: configData.metadata || {}
    };
  };

  const handleFormSave = async (configData: any) => {
    try {
      setFormLoading(true);
      setError(null);

      if (selectedConfig) {
        // Update existing config
        const requestBody = createRequestBody(configData);
        await updateProviderConfigs(selectedConfig.id, requestBody);

        // Refresh the entire list after successful update
        await fetchConfigs();
      } else {
        // Create new config
        const requestBody = createRequestBody(configData);
        await createProviderConfigs(requestBody);

        // Refresh the entire list after successful creation
        await fetchConfigs();
      }

      handleFormClose();
    } catch (error: any) {
      console.error("Error saving config:", error);

      // Enhanced error message based on error type
      let errorMessage = "Operation failed";
      if (error?.response?.status === 400) {
        errorMessage = error?.response?.data?.message || "Invalid configuration data. Please check your inputs.";
      } else if (error?.response?.status === 401) {
        errorMessage = "Authentication failed. Please log in again.";
      } else if (error?.response?.status === 403) {
        errorMessage = "You don't have permission to perform this action.";
      } else if (error?.response?.status === 409) {
        errorMessage = "A configuration with this name already exists.";
      } else if (error?.response?.status >= 500) {
        errorMessage = "Server error. Please try again later.";
      } else if (error?.code === 'NETWORK_ERROR') {
        errorMessage = "Network error. Please check your connection.";
      } else {
        errorMessage = error?.response?.data?.message || error?.message || "Operation failed";
      }

      setError(selectedConfig ? `Update failed: ${errorMessage}` : `Create failed: ${errorMessage}`);
    } finally {
      setFormLoading(false);
    }
  };

  const getActiveCount = () => {
    return configs.filter(config => config.is_active).length;
  };

  const getInactiveCount = () => {
    return configs.filter(config => !config.is_active).length;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 sm:p-6">
        <div className="flex flex-col space-y-4 lg:flex-row lg:items-center lg:justify-between lg:space-y-0">
          <div className="flex-1 min-w-0">
            <h2 className="text-lg sm:text-xl font-semibold text-gray-900">
              Provider Configurations
            </h2>
            <p className="text-sm text-gray-600 mt-1">
              Manage notification provider configurations and settings
            </p>
          </div>
          <div className="flex flex-col space-y-2 sm:flex-row sm:items-center sm:space-y-0 sm:space-x-4">
            <div className="bg-blue-50 px-4 py-2 rounded-lg">
              <span className="text-sm font-medium text-blue-700">
                Total Configurations: {configs.length}
              </span>
            </div>
            <div className="bg-green-50 px-4 py-2 rounded-lg">
              <span className="text-sm font-medium text-green-700">
                Active: {getActiveCount()}
              </span>
            </div>
            <div className="bg-red-50 px-4 py-2 rounded-lg">
              <span className="text-sm font-medium text-red-700">
                Inactive: {getInactiveCount()}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Responsive Actions Bar */}
      <div className="flex flex-col space-y-4 lg:flex-row lg:items-center lg:justify-between lg:space-y-0">
        <div className="flex flex-col space-y-2 sm:flex-row sm:items-center sm:space-y-0 sm:space-x-4">
          <button
            onClick={handleCreateConfig}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 sm:py-3 rounded-lg font-medium shadow-sm transition-colors whitespace-nowrap"
          >
            <i className="ri-add-line mr-2"></i>
            Add Configuration
          </button>
        </div>

        {/* Responsive Filters */}
        <div className="flex flex-col space-y-2 sm:flex-row sm:items-center sm:space-y-0 sm:space-x-4">
          <div className="relative">
            <i className="ri-search-line absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
            <input
              type="text"
              placeholder="Search Configurations..."
              className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm w-full sm:w-auto min-w-48"
            />
          </div>

          <select className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm w-full sm:w-auto pr-8">
            <option value="all">All Status</option>
            <option value="active">Active</option>
            <option value="inactive">Inactive</option>
          </select>

          <select className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm w-full sm:w-auto pr-8">
            <option value="">All Channels</option>
            <option value="email">Email</option>
            <option value="sms">SMS</option>
            <option value="whatsapp">WhatsApp</option>
            <option value="push">Push</option>
          </select>

          <select className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm w-full sm:w-auto pr-8">
            <option value="">All Providers</option>
            <option value="smtp">SMTP</option>
            <option value="msg91">MSG91</option>
            <option value="whatsapp">WhatsApp</option>
            <option value="fcm">FCM</option>
            <option value="push">Push</option>
          </select>
        </div>
      </div>

      {/* Provider Configs List */}
      {loading ? (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-12 text-center">
          <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <i className="ri-loader-line text-2xl text-blue-600 animate-spin"></i>
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Loading Configurations</h3>
          <p className="text-gray-600">Fetching provider configurations...</p>
        </div>
      ) : (
        <ProviderConfigsList
          configs={configs}
          onEdit={handleEditConfig}
          onView={handleViewConfig}
          onDelete={handleDeleteConfig}
          onCreate={handleCreateConfig}
          loading={formLoading}
        />
      )}

      {/* Provider Config View Modal */}
      {isFormOpen && isViewMode && (
        <ProviderConfigViewTabbedModal
          config={selectedConfig}
          isOpen={isFormOpen && isViewMode}
          onClose={handleFormClose}
          loading={viewLoading}
        />
      )}

      {/* Provider Config Form Modal */}
      {isFormOpen && !isViewMode && (
        <Modal
          isOpen={isFormOpen && !isViewMode}
          onClose={handleFormClose}
          title={selectedConfig ? 'Edit Provider Configuration' : 'Create Provider Configuration'}
          subtitle={selectedConfig ? `Editing configuration: ${selectedConfig.display_name}` : 'Create a new provider configuration'}
          size="xl"
          height="fixed"
          footer={
            <div className="flex items-center justify-between">
              <div className="flex items-center text-sm text-gray-500">
                {formLoading ? (
                  <>
                    <i className="ri-loader-line mr-1 animate-spin"></i>
                    {selectedConfig ? "Updating..." : "Creating..."}
                  </>
                ) : (
                  <>
                    <i className="ri-save-line mr-1"></i>
                    Auto-saved
                  </>
                )}
              </div>
              <div className="flex items-center space-x-3">
                <button
                  type="button"
                  onClick={handleFormClose}
                  disabled={formLoading}
                  className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  form="provider-config-form"
                  disabled={formLoading}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                >
                  {formLoading && <i className="ri-loader-line mr-2 animate-spin"></i>}
                  {selectedConfig ? 'Update Configuration' : 'Create Configuration'}
                </button>
              </div>
            </div>
          }
        >
          {error && (
            <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
              <div className="flex items-center">
                <i className="ri-error-warning-line text-red-600 mr-2"></i>
                <span className="text-red-700 text-sm">{error}</span>
                <button
                  onClick={() => setError(null)}
                  className="ml-auto text-red-600 hover:text-red-800"
                >
                  <i className="ri-close-line"></i>
                </button>
              </div>
            </div>
          )}
          <ProviderConfigForm
            config={selectedConfig}
            onSave={handleFormSave}
            onCancel={handleFormClose}
            loading={formLoading}
          />
        </Modal>
      )}
    </div>
  );
}

// Provider Config View Modal Component (inline like HotelMaster)
function ProviderConfigViewTabbedModal({
  config,
  isOpen,
  onClose,
  loading
}: {
  config: ProviderConfig | null;
  isOpen: boolean;
  onClose: () => void;
  loading: boolean;
}) {
  // Show loading state
  if (loading || !config) {
    return (
      <TabbedModal
        isOpen={isOpen}
        onClose={onClose}
        title="Loading Configuration..."
        subtitle="Please wait while we fetch the configuration details"
        tabs={[
          {
            id: "loading",
            label: "Loading",
            icon: "ri-loader-line",
            content: (
              <div className="p-12 text-center">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <i className="ri-loader-line text-2xl text-blue-600 animate-spin"></i>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Loading Configuration</h3>
                <p className="text-gray-600">Fetching the latest configuration data...</p>
              </div>
            )
          }
        ]}
        defaultTab="loading"
        size="full"
        height="fixed"
      />
    );
  }



  const getStatusBadge = (isActive?: boolean) => {
    return (
      <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${
        isActive
          ? 'bg-green-100 text-green-800 border border-green-200'
          : 'bg-red-100 text-red-800 border border-red-200'
      }`}>
        <div className={`w-2 h-2 rounded-full mr-2 ${
          isActive ? 'bg-green-500' : 'bg-red-500'
        }`}></div>
        {isActive ? 'Active' : 'Inactive'}
      </span>
    );
  };

  const getHealthStatusBadge = (status?: string) => {
    const statusConfig = {
      healthy: { color: 'bg-green-100 text-green-800 border-green-200', icon: 'ri-check-line' },
      unhealthy: { color: 'bg-red-100 text-red-800 border-red-200', icon: 'ri-close-line' },
      warning: { color: 'bg-yellow-100 text-yellow-800 border-yellow-200', icon: 'ri-alert-line' },
      unknown: { color: 'bg-gray-100 text-gray-800 border-gray-200', icon: 'ri-question-line' }
    };

    const statusStyle = statusConfig[status as keyof typeof statusConfig] || statusConfig.unknown;

    return (
      <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium border ${statusStyle.color}`}>
        <i className={`${statusStyle.icon} mr-2`}></i>
        {status || 'Unknown'}
      </span>
    );
  };

  const getChannelIcon = (channel: string) => {
    switch (channel.toLowerCase()) {
      case 'email':
        return 'ri-mail-line';
      case 'sms':
        return 'ri-message-2-line';
      case 'whatsapp':
        return 'ri-whatsapp-line';
      case 'push':
        return 'ri-notification-line';
      default:
        return 'ri-send-plane-line';
    }
  };

  const getChannelColor = (channel: string) => {
    switch (channel.toLowerCase()) {
      case 'email':
        return 'text-blue-600 bg-blue-100';
      case 'sms':
        return 'text-green-600 bg-green-100';
      case 'whatsapp':
        return 'text-green-600 bg-green-100';
      case 'push':
        return 'text-purple-600 bg-purple-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 4
    }).format(amount);
  };

  const getUsagePercentage = () => {
    if (!config.monthly_quota || !config.current_usage) return 0;
    return Math.round((config.current_usage / config.monthly_quota) * 100);
  };

  const tabs = [
    {
      id: 'overview',
      label: 'Overview',
      icon: 'ri-information-line',
      content: (
        <div className="p-6 space-y-6">
          {/* Provider Header */}
          <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl p-6 border border-blue-100">
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center space-x-4">
                <div className={`w-16 h-16 rounded-2xl flex items-center justify-center ${getChannelColor(config.channel)}`}>
                  <i className={`${getChannelIcon(config.channel)} text-2xl`}></i>
                </div>
                <div>
                  <h3 className="text-xl font-bold text-gray-900">{config.display_name}</h3>
                  <p className="text-gray-600 mt-1">{config.provider_type.toUpperCase()} • Priority {config.priority}</p>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                {getStatusBadge(config.is_active)}
                {getHealthStatusBadge(config.health_status)}
              </div>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="bg-white rounded-lg p-4 border border-blue-200">
                <div className="text-sm text-gray-600">Channel</div>
                <div className="font-semibold text-gray-900 capitalize">{config.channel}</div>
              </div>
              <div className="bg-white rounded-lg p-4 border border-blue-200">
                <div className="text-sm text-gray-600">Provider Type</div>
                <div className="font-semibold text-gray-900 uppercase">{config.provider_type}</div>
              </div>
              <div className="bg-white rounded-lg p-4 border border-blue-200">
                <div className="text-sm text-gray-600">Company ID</div>
                <div className="font-semibold text-gray-900">{config.company_id}</div>
              </div>
              <div className="bg-white rounded-lg p-4 border border-blue-200">
                <div className="text-sm text-gray-600">Priority</div>
                <div className="font-semibold text-gray-900">{config.priority}</div>
              </div>
            </div>
          </div>

          {/* Key Metrics */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
              <div className="flex items-center mb-4">
                <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                  <i className="ri-pulse-line text-green-600 text-lg"></i>
                </div>
                <h3 className="text-lg font-semibold text-gray-900">Health Status</h3>
              </div>
              <div className="space-y-4">
                <div className="flex justify-between items-center py-2 border-b border-gray-100">
                  <span className="text-sm text-gray-600">Current Status</span>
                  {getHealthStatusBadge(config.health_status)}
                </div>
                <div className="flex justify-between items-center py-2 border-b border-gray-100">
                  <span className="text-sm text-gray-600">Last Check</span>
                  <span className="text-sm font-medium text-gray-900">{formatDate(config.last_health_check)}</span>
                </div>
                <div className="flex justify-between items-center py-2">
                  <span className="text-sm text-gray-600">Check Interval</span>
                  <span className="text-sm font-medium text-gray-900">{config.health_check_interval}s</span>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
              <div className="flex items-center mb-4">
                <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                  <i className="ri-bar-chart-line text-purple-600 text-lg"></i>
                </div>
                <h3 className="text-lg font-semibold text-gray-900">Usage & Costs</h3>
              </div>
              <div className="space-y-4">
                <div className="flex justify-between items-center py-2 border-b border-gray-100">
                  <span className="text-sm text-gray-600">Current Usage</span>
                  <span className="text-sm font-medium text-gray-900">
                    {config.current_usage?.toLocaleString()} / {config.monthly_quota?.toLocaleString()}
                  </span>
                </div>
                <div className="flex justify-between items-center py-2 border-b border-gray-100">
                  <span className="text-sm text-gray-600">Usage %</span>
                  <span className="text-sm font-medium text-gray-900">{getUsagePercentage()}%</span>
                </div>
                <div className="flex justify-between items-center py-2">
                  <span className="text-sm text-gray-600">Cost per Message</span>
                  <span className="text-sm font-medium text-gray-900">{formatCurrency(config.cost_per_message)}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )
    },
    {
      id: 'configuration',
      label: 'Configuration',
      icon: 'ri-settings-line',
      content: (
        <div className="p-6 space-y-6">
          {/* Provider Configuration */}
          <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
            <div className="flex items-center mb-4">
              <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                <i className="ri-code-line text-green-600 text-lg"></i>
              </div>
              <h3 className="text-lg font-semibold text-gray-900">Provider Settings</h3>
            </div>
            <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
              <pre className="text-sm text-gray-900 whitespace-pre-wrap font-mono overflow-x-auto">
                {JSON.stringify(config.config, null, 2)}
              </pre>
            </div>
          </div>

          {/* Health Check Configuration */}
          <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
            <div className="flex items-center mb-4">
              <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                <i className="ri-heart-pulse-line text-blue-600 text-lg"></i>
              </div>
              <h3 className="text-lg font-semibold text-gray-900">Health Check</h3>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div className="flex justify-between items-center py-3 border-b border-gray-100">
                  <span className="text-sm font-medium text-gray-600">Health Check URL</span>
                  <span className="text-sm text-gray-900 font-mono break-all">
                    {config.health_check_url}
                  </span>
                </div>
                <div className="flex justify-between items-center py-3 border-b border-gray-100">
                  <span className="text-sm font-medium text-gray-600">Check Interval</span>
                  <span className="text-sm text-gray-900">{config.health_check_interval}s</span>
                </div>
                <div className="flex justify-between items-center py-3">
                  <span className="text-sm font-medium text-gray-600">Timeout</span>
                  <span className="text-sm text-gray-900">{config.timeout_seconds}s</span>
                </div>
              </div>
              <div className="space-y-4">
                <div className="flex justify-between items-center py-3 border-b border-gray-100">
                  <span className="text-sm font-medium text-gray-600">Current Status</span>
                  {getHealthStatusBadge(config.health_status)}
                </div>
                <div className="flex justify-between items-center py-3 border-b border-gray-100">
                  <span className="text-sm font-medium text-gray-600">Last Check</span>
                  <span className="text-sm text-gray-900">{formatDate(config.last_health_check)}</span>
                </div>
                <div className="flex justify-between items-center py-3">
                  <span className="text-sm font-medium text-gray-600">Next Check</span>
                  <span className="text-sm text-gray-900">
                    {config.last_health_check && config.health_check_interval
                      ? formatDate(new Date(new Date(config.last_health_check).getTime() + (config.health_check_interval * 1000)).toISOString())
                      : 'N/A'
                    }
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Circuit Breaker Configuration */}
          <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
            <div className="flex items-center mb-4">
              <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center mr-3">
                <i className="ri-shield-line text-orange-600 text-lg"></i>
              </div>
              <h3 className="text-lg font-semibold text-gray-900">Circuit Breaker</h3>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-orange-50 rounded-lg p-4">
                <div className="text-xs text-orange-500 uppercase tracking-wide">Failure Threshold</div>
                <div className="text-lg font-semibold text-orange-900 mt-1">
                  {config.circuit_breaker_threshold}
                </div>
              </div>
              <div className="bg-orange-50 rounded-lg p-4">
                <div className="text-xs text-orange-500 uppercase tracking-wide">Timeout</div>
                <div className="text-lg font-semibold text-orange-900 mt-1">
                  {config.circuit_breaker_timeout}s
                </div>
              </div>
              <div className="bg-orange-50 rounded-lg p-4">
                <div className="text-xs text-orange-500 uppercase tracking-wide">Current Failures</div>
                <div className="text-lg font-semibold text-orange-900 mt-1">
                  {config.retry_count || 0}
                </div>
              </div>
            </div>
          </div>
        </div>
      )
    },
    {
      id: 'performance',
      label: 'Performance',
      icon: 'ri-bar-chart-line',
      content: (
        <div className="p-6 space-y-6">
          {/* Usage Statistics */}
          <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
            <div className="flex items-center mb-4">
              <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                <i className="ri-line-chart-line text-purple-600 text-lg"></i>
              </div>
              <h3 className="text-lg font-semibold text-gray-900">Usage Statistics</h3>
            </div>

            {/* Usage Progress */}
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium text-gray-600">Monthly Usage</span>
                <span className="text-sm font-semibold text-gray-900">
                  {config.current_usage?.toLocaleString()} / {config.monthly_quota?.toLocaleString()}
                </span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-3">
                <div
                  className="bg-purple-600 h-3 rounded-full transition-all duration-300"
                  style={{ width: `${Math.min(getUsagePercentage(), 100)}%` }}
                ></div>
              </div>
              <div className="flex justify-between text-sm text-gray-500">
                <span>{getUsagePercentage()}% used</span>
                <span>Resets: {formatDate(config.usage_reset_date)}</span>
              </div>
            </div>

            {/* Cost Analysis */}
            <div className="mt-6 pt-6 border-t border-gray-200">
              <h4 className="text-md font-semibold text-gray-900 mb-4">Cost Analysis</h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-purple-50 rounded-lg p-4">
                  <div className="text-xs text-purple-500 uppercase tracking-wide">Cost per Message</div>
                  <div className="text-lg font-semibold text-purple-900 mt-1">
                    {formatCurrency(config.cost_per_message)}
                  </div>
                </div>
                <div className="bg-purple-50 rounded-lg p-4">
                  <div className="text-xs text-purple-500 uppercase tracking-wide">Monthly Spend</div>
                  <div className="text-lg font-semibold text-purple-900 mt-1">
                    {formatCurrency((config.current_usage || 0) * config.cost_per_message)}
                  </div>
                </div>
                <div className="bg-purple-50 rounded-lg p-4">
                  <div className="text-xs text-purple-500 uppercase tracking-wide">Projected Monthly</div>
                  <div className="text-lg font-semibold text-purple-900 mt-1">
                    {formatCurrency((config.monthly_quota || 0) * config.cost_per_message)}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Rate Limits & Quotas */}
          <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
            <div className="flex items-center mb-4">
              <div className="w-10 h-10 bg-indigo-100 rounded-lg flex items-center justify-center mr-3">
                <i className="ri-speed-line text-indigo-600 text-lg"></i>
              </div>
              <h3 className="text-lg font-semibold text-gray-900">Rate Limits & Quotas</h3>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="bg-indigo-50 rounded-lg p-4">
                <div className="text-xs text-indigo-500 uppercase tracking-wide">Rate Limit</div>
                <div className="text-lg font-semibold text-indigo-900 mt-1">
                  {config.rate_limit}/min
                </div>
              </div>
              <div className="bg-indigo-50 rounded-lg p-4">
                <div className="text-xs text-indigo-500 uppercase tracking-wide">Retry Limit</div>
                <div className="text-lg font-semibold text-indigo-900 mt-1">
                  {config.retry_limit}
                </div>
              </div>
              <div className="bg-indigo-50 rounded-lg p-4">
                <div className="text-xs text-indigo-500 uppercase tracking-wide">Retry Delay</div>
                <div className="text-lg font-semibold text-indigo-900 mt-1">
                  {config.retry_delay}ms
                </div>
              </div>
              <div className="bg-indigo-50 rounded-lg p-4">
                <div className="text-xs text-indigo-500 uppercase tracking-wide">Priority</div>
                <div className="text-lg font-semibold text-indigo-900 mt-1">
                  {config.priority}
                </div>
              </div>
            </div>
          </div>
        </div>
      )
    },
    {
      id: 'details',
      label: 'Details',
      icon: 'ri-information-line',
      content: (
        <div className="p-6 space-y-6">
          {/* Basic Details */}
          <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
            <div className="flex items-center mb-4">
              <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center mr-3">
                <i className="ri-file-text-line text-gray-600 text-lg"></i>
              </div>
              <h3 className="text-lg font-semibold text-gray-900">Basic Details</h3>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div className="flex justify-between items-center py-3 border-b border-gray-100">
                  <span className="text-sm font-medium text-gray-600">Configuration ID</span>
                  <span className="text-sm text-gray-900 font-mono">{config.id}</span>
                </div>
                <div className="flex justify-between items-center py-3 border-b border-gray-100">
                  <span className="text-sm font-medium text-gray-600">Company ID</span>
                  <span className="text-sm text-gray-900">{config.company_id}</span>
                </div>
                <div className="flex justify-between items-center py-3 border-b border-gray-100">
                  <span className="text-sm font-medium text-gray-600">Retry Count</span>
                  <span className="text-sm text-gray-900">{config.retry_count}</span>
                </div>
                <div className="flex justify-between items-center py-3">
                  <span className="text-sm font-medium text-gray-600">Usage Reset Date</span>
                  <span className="text-sm text-gray-900">{config.usage_reset_date}</span>
                </div>
              </div>
              <div className="space-y-4">
                <div className="flex justify-between items-center py-3 border-b border-gray-100">
                  <span className="text-sm font-medium text-gray-600">Created At</span>
                  <span className="text-sm text-gray-900">{formatDate(config.created_at)}</span>
                </div>
                <div className="flex justify-between items-center py-3 border-b border-gray-100">
                  <span className="text-sm font-medium text-gray-600">Updated At</span>
                  <span className="text-sm text-gray-900">{formatDate(config.updated_at)}</span>
                </div>
                <div className="flex justify-between items-center py-3 border-b border-gray-100">
                  <span className="text-sm font-medium text-gray-600">Active Status</span>
                  {getStatusBadge(config.is_active)}
                </div>
                <div className="flex justify-between items-center py-3">
                  <span className="text-sm font-medium text-gray-600">Health Status</span>
                  {getHealthStatusBadge(config.health_status)}
                </div>
              </div>
            </div>
          </div>

          {/* Tags */}
          <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
            <div className="flex items-center mb-4">
              <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                <i className="ri-price-tag-3-line text-blue-600 text-lg"></i>
              </div>
              <h3 className="text-lg font-semibold text-gray-900">Tags</h3>
            </div>
            <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
              <pre className="text-sm text-gray-900 whitespace-pre-wrap font-mono overflow-x-auto">
                {JSON.stringify(config.tags, null, 2)}
              </pre>
            </div>
          </div>

          {/* Metadata */}
          <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
            <div className="flex items-center mb-4">
              <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                <i className="ri-database-line text-green-600 text-lg"></i>
              </div>
              <h3 className="text-lg font-semibold text-gray-900">Metadata</h3>
            </div>
            <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
              <pre className="text-sm text-gray-900 whitespace-pre-wrap font-mono overflow-x-auto">
                {JSON.stringify(config.metadata, null, 2)}
              </pre>
            </div>
          </div>
        </div>
      )
    }
  ];

  return (
    <TabbedModal
      isOpen={isOpen}
      onClose={onClose}
      title={`${config.display_name}`}
      subtitle={`${config.provider_type.toUpperCase()} • ${config.channel.toUpperCase()} • Priority ${config.priority}`}
      tabs={tabs}
      defaultTab="overview"
      size="full"
      height="fixed"
      headerActions={
        <div className="flex items-center space-x-2">
          {getStatusBadge(config.is_active)}
          {getHealthStatusBadge(config.health_status)}
          <div className={`inline-flex items-center px-3 py-2 rounded-lg text-sm font-medium ${getChannelColor(config.channel)}`}>
            <i className={`${getChannelIcon(config.channel)} mr-2`}></i>
            {config.channel.toUpperCase()}
          </div>
        </div>
      }
    />
  );
}
