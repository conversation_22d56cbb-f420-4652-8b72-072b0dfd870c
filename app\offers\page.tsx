'use client';

import OfferMaster from './components/OfferMaster';

export default function OffersPage() {
  return (
    <div className="h-full flex flex-col">
      {/* Enhanced Professional Header */}
      <div className="flex-shrink-0 bg-white border-b border-gray-200">
        <div className="px-6 py-6">
          <div className="flex flex-col space-y-4 lg:flex-row lg:items-center lg:justify-between lg:space-y-0">
            <div className="flex-1 min-w-0">
              <div className="flex items-center space-x-3 mb-2">
                <div className="w-10 h-10 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center">
                  <i className="ri-gift-line text-white text-lg"></i>
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">
                    Offer Management
                  </h1>
                  <p className="text-sm text-gray-600">
                    Create, manage, and track special offers for your travel services
                  </p>
                </div>
              </div>
              
              {/* Quick Stats */}
              <div className="flex items-center space-x-6 text-sm text-gray-500">
                <div className="flex items-center space-x-1">
                  <i className="ri-percent-line"></i>
                  <span>Promotional Deals</span>
                </div>
                <div className="flex items-center space-x-1">
                  <i className="ri-calendar-check-line"></i>
                  <span>Time-limited Offers</span>
                </div>
                <div className="flex items-center space-x-1">
                  <i className="ri-user-line"></i>
                  <span>Targeted Promotions</span>
                </div>
                <div className="flex items-center space-x-1">
                  <i className="ri-hotel-line"></i>
                  <span>Service-specific Deals</span>
                </div>
              </div>
            </div>
            
            {/* Header Actions */}
            <div className="flex items-center space-x-3">
              <div className="bg-orange-50 px-4 py-2 rounded-lg">
                <div className="flex items-center space-x-2">
                  <i className="ri-information-line text-orange-600"></i>
                  <span className="text-sm font-medium text-orange-700">
                    Manage Promotions
                  </span>
                </div>
              </div>
              
              {/* Quick Actions */}
              <div className="flex items-center space-x-2">
                <button className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors">
                  <i className="ri-download-line" title="Export Offers"></i>
                </button>
                <button className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors">
                  <i className="ri-refresh-line" title="Refresh Data"></i>
                </button>
                <button className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors">
                  <i className="ri-settings-3-line" title="Settings"></i>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="flex-1 overflow-y-auto bg-gray-50">
        <div className="p-6">
          <div className="max-w-full">
            <OfferMaster />
          </div>
        </div>
      </div>
    </div>
  );
}
