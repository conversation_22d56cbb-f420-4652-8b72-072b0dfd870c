'use client';

import { useState } from 'react';
import FaqMaster from './components/FaqMaster';

export default function FaqPage() {
  const [lastSyncTime] = useState(new Date().toLocaleTimeString());

  return (
    <div className="h-full flex flex-col">
      {/* Enhanced Professional Header */}
      <div className="flex-shrink-0 bg-white border-b border-gray-200">
        <div className="px-6 py-6">
          <div className="flex flex-col space-y-4 lg:flex-row lg:items-center lg:justify-between lg:space-y-0">
            <div className="flex-1 min-w-0">
              <div className="flex items-center space-x-3 mb-2">
                <div className="w-10 h-10 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center">
                  <i className="ri-question-line text-white text-lg"></i>
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">
                    FAQ Management
                  </h1>
                  <p className="text-sm text-gray-600">
                    Man<PERSON> frequently asked questions and answers for all services and languages
                  </p>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex items-center space-x-3">
              <div className="hidden sm:flex items-center space-x-3">
                <div className="bg-gray-50 px-3 py-2 rounded-lg">
                  <div className="flex items-center text-sm">
                    <i className="ri-time-line text-gray-400 mr-2"></i>
                    <span className="text-gray-600">Last sync: {lastSyncTime}</span>
                  </div>
                </div>
                <div className="bg-green-50 px-3 py-2 rounded-lg">
                  <div className="flex items-center text-sm">
                    <div className="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                    <span className="text-green-700 font-medium">System Online</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

    
        </div>
      </div>

      {/* Content Area with Internal Scrolling */}
      <div className="flex-1 overflow-y-auto custom-scrollbar bg-gray-50">
        <div className="p-6">
          <div className="max-w-full">
            <div className="animate-fade-in">
              <FaqMaster />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
