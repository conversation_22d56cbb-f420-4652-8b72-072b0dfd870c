// 'use client';

// import { useState } from 'react';

// interface AppStoreLink {
//   platform: 'ios' | 'android';
//   url: string;
//   enabled: boolean;
// }

// interface MobileAppImage {
//   id: string;
//   url: string;
//   alt: string;
//   enabled: boolean;
// }

// export default function SettingsPage() {
//   const [lastSyncTime] = useState(new Date().toLocaleTimeString());
  
//   // App Store Links State
//   const [appStoreLinks, setAppStoreLinks] = useState<AppStoreLink[]>([
//     { platform: 'ios', url: 'https://apps.apple.com/app/your-app', enabled: true },
//     { platform: 'android', url: 'https://play.google.com/store/apps/details?id=your.app', enabled: true }
//   ]);

//   // Mobile App Images State
//   const [mobileAppImages, setMobileAppImages] = useState<MobileAppImage[]>([
//     { id: '1', url: '/images/app-banner-1.jpg', alt: 'App Banner 1', enabled: true },
//     { id: '2', url: '/images/app-banner-2.jpg', alt: 'App Banner 2', enabled: false },
//     { id: '3', url: '/images/app-banner-3.jpg', alt: 'App Banner 3', enabled: true }
//   ]);

//   // Markup Settings State
//   const [markupSettings, setMarkupSettings] = useState({
//     enableMarkupsOnList: true,
//     markupCalculationBase: 'base_price' as 'base_price' | 'total_price'
//   });

//   // Handlers
//   const handleAppStoreLinkToggle = (platform: 'ios' | 'android') => {
//     setAppStoreLinks(prev => 
//       prev.map(link => 
//         link.platform === platform 
//           ? { ...link, enabled: !link.enabled }
//           : link
//       )
//     );
//   };

//   const handleAppStoreLinkUpdate = (platform: 'ios' | 'android', url: string) => {
//     setAppStoreLinks(prev => 
//       prev.map(link => 
//         link.platform === platform 
//           ? { ...link, url }
//           : link
//       )
//     );
//   };

//   const handleImageToggle = (id: string) => {
//     setMobileAppImages(prev => 
//       prev.map(img => 
//         img.id === id 
//           ? { ...img, enabled: !img.enabled }
//           : img
//       )
//     );
//   };

//   const handleImageUpload = () => {
//     alert('Image upload functionality - integrate with your file upload service');
//   };

//   const handleImageDelete = (id: string) => {
//     if (confirm('Are you sure you want to delete this image?')) {
//       setMobileAppImages(prev => prev.filter(img => img.id !== id));
//     }
//   };

//   const handleSaveSettings = () => {
//     alert('Settings saved successfully!');
//     console.log('Saved Settings:', {
//       appStoreLinks,
//       mobileAppImages,
//       markupSettings
//     });
//   };

//   return (
//     <div className="h-full flex flex-col">
//       {/* Enhanced Professional Header */}
//       <div className="flex-shrink-0 bg-white border-b border-gray-200">
//         <div className="px-6 py-6">
//           <div className="flex flex-col space-y-4 lg:flex-row lg:items-center lg:justify-between lg:space-y-0">
//             <div className="flex-1 min-w-0">
//               <div className="flex items-center space-x-3 mb-2">
//                 <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center">
//                   <i className="ri-settings-3-line text-white text-lg"></i>
//                 </div>
//                 <div>
//                   <h1 className="text-2xl font-bold text-gray-900">
//                     System Settings
//                   </h1>
//                   <p className="text-sm text-gray-600">
//                     Configure app stores, mobile images, and markup preferences
//                   </p>
//                 </div>
//               </div>
//             </div>

//             {/* Header Actions */}
//             <div className="flex items-center space-x-3">
//               <div className="hidden sm:flex items-center space-x-2 text-sm text-gray-500">
//                 <i className="ri-time-line"></i>
//                 <span>Last sync: {lastSyncTime}</span>
//               </div>
//               <button className="inline-flex items-center px-4 py-2 bg-gray-100 text-gray-700 rounded-lg font-medium hover:bg-gray-200 transition-colors shadow-sm">
//                 <i className="ri-refresh-line mr-2"></i>
//                 Reset
//               </button>
//               <button 
//                 onClick={handleSaveSettings}
//                 className="inline-flex items-center px-4 py-2 bg-purple-600 text-white rounded-lg font-medium hover:bg-purple-700 transition-colors shadow-sm"
//               >
//                 <i className="ri-save-line mr-2"></i>
//                 Save Changes
//               </button>
//             </div>
//           </div>

//           {/* Quick Stats */}
//           <div className="mt-4 grid grid-cols-2 sm:grid-cols-4 gap-4">
//             <div className="bg-gray-50 rounded-lg p-3">
//               <div className="flex items-center">
//                 <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
//                   <i className="ri-smartphone-line text-blue-600 text-sm"></i>
//                 </div>
//                 <div>
//                   <p className="text-xs text-gray-600">App Store Links</p>
//                   <p className="text-lg font-semibold text-gray-900">
//                     {appStoreLinks.filter(l => l.enabled).length}/{appStoreLinks.length}
//                   </p>
//                 </div>
//               </div>
//             </div>
//             <div className="bg-gray-50 rounded-lg p-3">
//               <div className="flex items-center">
//                 <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mr-3">
//                   <i className="ri-image-line text-green-600 text-sm"></i>
//                 </div>
//                 <div>
//                   <p className="text-xs text-gray-600">Active Images</p>
//                   <p className="text-lg font-semibold text-gray-900">
//                     {mobileAppImages.filter(i => i.enabled).length}
//                   </p>
//                 </div>
//               </div>
//             </div>
//             <div className="bg-gray-50 rounded-lg p-3">
//               <div className="flex items-center">
//                 <div className="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center mr-3">
//                   <i className="ri-price-tag-3-line text-yellow-600 text-sm"></i>
//                 </div>
//                 <div>
//                   <p className="text-xs text-gray-600">Markup Display</p>
//                   <p className="text-lg font-semibold text-gray-900">
//                     {markupSettings.enableMarkupsOnList ? 'ON' : 'OFF'}
//                   </p>
//                 </div>
//               </div>
//             </div>
//             <div className="bg-gray-50 rounded-lg p-3">
//               <div className="flex items-center">
//                 <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
//                   <i className="ri-calculator-line text-purple-600 text-sm"></i>
//                 </div>
//                 <div>
//                   <p className="text-xs text-gray-600">Calculation Base</p>
//                   <p className="text-lg font-semibold text-gray-900">
//                     {markupSettings.markupCalculationBase === 'base_price' ? 'Base' : 'Total'}
//                   </p>
//                 </div>
//               </div>
//             </div>
//           </div>
//         </div>
//       </div>

//       {/* Content Area with Internal Scrolling */}
//       <div className="flex-1 overflow-y-auto custom-scrollbar bg-gray-50">
//         <div className="p-6">
//           <div className="max-w-full space-y-6">
            
//             {/* App Store Links Section */}
//             <div className="animate-fade-in">
//               <div className="bg-white rounded-lg shadow-sm border border-gray-200">
//                 <div className="p-6 border-b border-gray-200">
//                   <div className="flex items-center space-x-3">
//                     <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
//                       <i className="ri-smartphone-line text-blue-600 text-lg"></i>
//                     </div>
//                     <div>
//                       <h2 className="text-lg font-semibold text-gray-900">App Store Links</h2>
//                       <p className="text-sm text-gray-600">Manage mobile application download links</p>
//                     </div>
//                   </div>
//                 </div>
//                 <div className="p-6">
//                   <div className="space-y-4">
//                     {appStoreLinks.map((link) => (
//                       <div key={link.platform} className="flex items-center space-x-4 p-4 bg-gray-50 rounded-lg border border-gray-200">
//                         <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${
//                           link.platform === 'ios' ? 'bg-black' : 'bg-green-500'
//                         }`}>
//                           <i className={`${
//                             link.platform === 'ios' ? 'ri-apple-fill' : 'ri-google-play-fill'
//                           } text-white text-xl`}></i>
//                         </div>
//                         <div className="flex-1">
//                           <label className="block text-sm font-medium text-gray-700 mb-2">
//                             {link.platform === 'ios' ? 'App Store' : 'Play Store'} URL
//                           </label>
//                           <input
//                             type="url"
//                             value={link.url}
//                             onChange={(e) => handleAppStoreLinkUpdate(link.platform, e.target.value)}
//                             placeholder={`Enter ${link.platform === 'ios' ? 'App Store' : 'Play Store'} URL`}
//                             className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
//                           />
//                         </div>
//                         <div className="flex items-center">
//                           <button
//                             onClick={() => handleAppStoreLinkToggle(link.platform)}
//                             className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
//                               link.enabled ? 'bg-purple-600' : 'bg-gray-300'
//                             }`}
//                           >
//                             <span
//                               className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
//                                 link.enabled ? 'translate-x-6' : 'translate-x-1'
//                               }`}
//                             />
//                           </button>
//                           <span className="ml-3 text-sm font-medium text-gray-700">
//                             {link.enabled ? 'Enabled' : 'Disabled'}
//                           </span>
//                         </div>
//                       </div>
//                     ))}
//                   </div>
//                 </div>
//               </div>
//             </div>

//             {/* Mobile App Images Section */}
//             <div className="animate-fade-in">
//               <div className="bg-white rounded-lg shadow-sm border border-gray-200">
//                 <div className="p-6 border-b border-gray-200">
//                   <div className="flex items-center justify-between">
//                     <div className="flex items-center space-x-3">
//                       <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
//                         <i className="ri-image-line text-green-600 text-lg"></i>
//                       </div>
//                       <div>
//                         <h2 className="text-lg font-semibold text-gray-900">Mobile App Images</h2>
//                         <p className="text-sm text-gray-600">Manage promotional images for mobile app</p>
//                       </div>
//                     </div>
//                     <button
//                       onClick={handleImageUpload}
//                       className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg font-medium hover:bg-green-700 transition-colors"
//                     >
//                       <i className="ri-upload-2-line mr-2"></i>
//                       Upload Image
//                     </button>
//                   </div>
//                 </div>
//                 <div className="p-6">
//                   <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
//                     {mobileAppImages.map((image) => (
//                       <div key={image.id} className="bg-gray-50 rounded-lg border border-gray-200 overflow-hidden">
//                         <div className="aspect-video bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center">
//                           <i className="ri-image-line text-gray-400 text-4xl"></i>
//                         </div>
//                         <div className="p-4">
//                           <div className="flex items-center justify-between mb-3">
//                             <span className="text-sm font-medium text-gray-900">{image.alt}</span>
//                             <div className="flex items-center space-x-2">
//                               <button
//                                 onClick={() => handleImageToggle(image.id)}
//                                 className={`relative inline-flex h-5 w-9 items-center rounded-full transition-colors ${
//                                   image.enabled ? 'bg-green-600' : 'bg-gray-300'
//                                 }`}
//                               >
//                                 <span
//                                   className={`inline-block h-3 w-3 transform rounded-full bg-white transition-transform ${
//                                     image.enabled ? 'translate-x-5' : 'translate-x-1'
//                                   }`}
//                                 />
//                               </button>
//                             </div>
//                           </div>
//                           <div className="flex items-center justify-between">
//                             <span className={`text-xs font-medium ${
//                               image.enabled ? 'text-green-600' : 'text-gray-500'
//                             }`}>
//                               {image.enabled ? 'Active' : 'Inactive'}
//                             </span>
//                             <div className="flex items-center space-x-2">
//                               <button
//                                 className="text-blue-600 hover:text-blue-800 transition-colors"
//                                 title="Edit"
//                               >
//                                 <i className="ri-edit-line text-lg"></i>
//                               </button>
//                               <button
//                                 onClick={() => handleImageDelete(image.id)}
//                                 className="text-red-600 hover:text-red-800 transition-colors"
//                                 title="Delete"
//                               >
//                                 <i className="ri-delete-bin-line text-lg"></i>
//                               </button>
//                             </div>
//                           </div>
//                         </div>
//                       </div>
//                     ))}
//                   </div>
//                 </div>
//               </div>
//             </div>

//             {/* Markup Configuration Section */}
//             <div className="animate-fade-in">
//               <div className="bg-white rounded-lg shadow-sm border border-gray-200">
//                 <div className="p-6 border-b border-gray-200">
//                   <div className="flex items-center space-x-3">
//                     <div className="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center">
//                       <i className="ri-price-tag-3-line text-yellow-600 text-lg"></i>
//                     </div>
//                     <div>
//                       <h2 className="text-lg font-semibold text-gray-900">Markup & Offers Configuration</h2>
//                       <p className="text-sm text-gray-600">Configure markup display and calculation preferences</p>
//                     </div>
//                   </div>
//                 </div>
//                 <div className="p-6">
//                   <div className="space-y-6">
//                     {/* Enable/Disable Markups on List Page */}
//                     <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg border border-gray-200">
//                       <div className="flex-1">
//                         <h3 className="text-sm font-semibold text-gray-900 mb-1">
//                           Display Markups on List Page
//                         </h3>
//                         <p className="text-sm text-gray-600">
//                           Show or hide markup information on the hotel/service listing pages
//                         </p>
//                       </div>
//                       <div className="flex items-center ml-4">
//                         <button
//                           onClick={() => setMarkupSettings(prev => ({
//                             ...prev,
//                             enableMarkupsOnList: !prev.enableMarkupsOnList
//                           }))}
//                           className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
//                             markupSettings.enableMarkupsOnList ? 'bg-purple-600' : 'bg-gray-300'
//                           }`}
//                         >
//                           <span
//                             className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
//                               markupSettings.enableMarkupsOnList ? 'translate-x-6' : 'translate-x-1'
//                             }`}
//                           />
//                         </button>
//                         <span className="ml-3 text-sm font-medium text-gray-700">
//                           {markupSettings.enableMarkupsOnList ? 'Enabled' : 'Disabled'}
//                         </span>
//                       </div>
//                     </div>

//                     {/* Markup Calculation Base */}
//                     <div className="p-4 bg-gray-50 rounded-lg border border-gray-200">
//                       <h3 className="text-sm font-semibold text-gray-900 mb-3">
//                         Markup Calculation Base
//                       </h3>
//                       <p className="text-sm text-gray-600 mb-4">
//                         Choose whether markup should be calculated on the base price or total price (including taxes and fees)
//                       </p>
//                       <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
//                         <button
//                           onClick={() => setMarkupSettings(prev => ({
//                             ...prev,
//                             markupCalculationBase: 'base_price'
//                           }))}
//                           className={`p-4 rounded-lg border-2 transition-all ${
//                             markupSettings.markupCalculationBase === 'base_price'
//                               ? 'border-purple-600 bg-purple-50'
//                               : 'border-gray-200 bg-white hover:border-gray-300'
//                           }`}
//                         >
//                           <div className="flex items-center space-x-3">
//                             <div className={`w-5 h-5 rounded-full border-2 flex items-center justify-center ${
//                               markupSettings.markupCalculationBase === 'base_price'
//                                 ? 'border-purple-600'
//                                 : 'border-gray-300'
//                             }`}>
//                               {markupSettings.markupCalculationBase === 'base_price' && (
//                                 <div className="w-3 h-3 rounded-full bg-purple-600"></div>
//                               )}
//                             </div>
//                             <div className="text-left flex-1">
//                               <div className="font-semibold text-gray-900">Base Price</div>
//                               <div className="text-xs text-gray-600 mt-1">
//                                 Calculate markup on room/service base price only
//                               </div>
//                             </div>
//                           </div>
//                         </button>

//                         <button
//                           onClick={() => setMarkupSettings(prev => ({
//                             ...prev,
//                             markupCalculationBase: 'total_price'
//                           }))}
//                           className={`p-4 rounded-lg border-2 transition-all ${
//                             markupSettings.markupCalculationBase === 'total_price'
//                               ? 'border-purple-600 bg-purple-50'
//                               : 'border-gray-200 bg-white hover:border-gray-300'
//                           }`}
//                         >
//                           <div className="flex items-center space-x-3">
//                             <div className={`w-5 h-5 rounded-full border-2 flex items-center justify-center ${
//                               markupSettings.markupCalculationBase === 'total_price'
//                                 ? 'border-purple-600'
//                                 : 'border-gray-300'
//                             }`}>
//                               {markupSettings.markupCalculationBase === 'total_price' && (
//                                 <div className="w-3 h-3 rounded-full bg-purple-600"></div>
//                               )}
//                             </div>
//                             <div className="text-left flex-1">
//                               <div className="font-semibold text-gray-900">Total Price</div>
//                               <div className="text-xs text-gray-600 mt-1">
//                                 Calculate markup on total price (base + taxes + fees)
//                               </div>
//                             </div>
//                           </div>
//                         </button>
//                       </div>
//                     </div>

//                     {/* Example Calculation */}
//                     <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
//                       <div className="flex items-start space-x-3">
//                         <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
//                           <i className="ri-information-line text-blue-600"></i>
//                         </div>
//                         <div className="flex-1">
//                           <h4 className="text-sm font-semibold text-blue-900 mb-2">Example Calculation</h4>
//                           <div className="text-sm text-blue-800 space-y-1">
//                             <p>Base Price: $100 | Taxes & Fees: $20 | Markup: 10%</p>
//                             <p className="font-semibold mt-2">
//                               {markupSettings.markupCalculationBase === 'base_price' 
//                                 ? '• Base Price Calculation: $100 × 10% = $10 markup → Total: $130'
//                                 : '• Total Price Calculation: $120 × 10% = $12 markup → Total: $132'
//                               }
//                             </p>
//                           </div>
//                         </div>
//                       </div>
//                     </div>
//                   </div>
//                 </div>
//               </div>
//             </div>

//           </div>
//         </div>
//       </div>
//     </div>
//   );
// }

'use client';

import { useEffect, useState } from 'react';

import AppStoreLinks from './components/AppStoreLinks';
import MobileAppImages from './components/MobileAppImages';
import MarkupOffersConfig from './components/MarkupOffersConfig';
import { SettingsResponse, UpdateSettingsRequest } from './settings.model';
import { settingsService } from './settings-service';

// --- Fallback Mock Data ---
const MOCK_SETTINGS: SettingsResponse = {
  app_store_url: "https://apps.apple.com/app/your-app/id123456789",
  enable_app_store: true,
  play_store_url: "https://play.google.com/store/apps/details?id=com.your.package",
  enable_play_store: true,
  mobile_app_image_url: "/placeholder.png", // Local placeholder
  enable_markups: true,
  enable_offers: false,
  markup_calculation_basis: "total",
};
// --------------------------

export default function SettingsPage() {
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [settings, setSettings] = useState<SettingsResponse | null>(null);
  const [mobileAppFile, setMobileAppFile] = useState<File | null>(null);

  // Fetch settings on mount
  useEffect(() => {
    async function fetchSettings() {
      try {
        setLoading(true);
        const data = await settingsService.getSettings();
        setSettings(data);
      } catch (error) {
        console.error('Error fetching settings, using mock data', error);
        setSettings(MOCK_SETTINGS);
      } finally {
        setLoading(false);
      }
    }
    fetchSettings();
  }, []);

  // Toggle App Store
  const handleAppStoreToggle = (platform: 'ios' | 'android') => {
    if (!settings) return;
    setSettings({
      ...settings,
      enable_app_store: platform === 'ios' ? !settings.enable_app_store : settings.enable_app_store,
      enable_play_store: platform === 'android' ? !settings.enable_play_store : settings.enable_play_store,
    });
  };

  // Update App Store URL
  const handleAppStoreUrlChange = (platform: 'ios' | 'android', url: string) => {
    if (!settings) return;
    setSettings({
      ...settings,
      app_store_url: platform === 'ios' ? url : settings.app_store_url,
      play_store_url: platform === 'android' ? url : settings.play_store_url,
    });
  };

  // Toggle Markups
  const handleMarkupToggle = () => {
    if (!settings) return;
    setSettings({ ...settings, enable_markups: !settings.enable_markups });
  };

  // Change Markup Base
  const handleMarkupBaseChange = (base: 'base' | 'total') => {
    if (!settings) return;
    setSettings({ ...settings, markup_calculation_basis: base });
  };

  // Handle Mobile App Image
  const handleImageChange = (file: File) => {
    setMobileAppFile(file);
    if (settings) {
      setSettings({ ...settings, mobile_app_image_url: URL.createObjectURL(file) });
    }
  };

  const handleImageDelete = () => {
    setMobileAppFile(null);
    if (settings) setSettings({ ...settings, mobile_app_image_url: '' });
  };

  // Save settings
  const handleSave = async () => {
    if (!settings) return;
    try {
      setSaving(true);
      const payload: UpdateSettingsRequest = {
        app_store_url: settings.app_store_url,
        enable_app_store: settings.enable_app_store,
        play_store_url: settings.play_store_url,
        enable_play_store: settings.enable_play_store,
        enable_markups: settings.enable_markups,
        enable_offers: settings.enable_offers,
        markup_calculation_basis: settings.markup_calculation_basis,
        mobile_app_image: mobileAppFile || null,
      };

      const updated = await settingsService.updateSettings(payload);
      setSettings(updated);
      setMobileAppFile(null);
      alert('Settings updated successfully!');
    } catch (error) {
      console.error('Error saving settings', error);
      alert('Failed to update settings.');
    } finally {
      setSaving(false);
    }
  };

  if (loading) return <div className="p-6">Loading...</div>;
  if (!settings) return <div className="p-6">Error: Could not load settings.</div>;

  return (
    <div className="p-6 space-y-8">
      {/* App Store Links */}
      <AppStoreLinks
        appStoreLinks={[
          { platform: 'ios', url: settings.app_store_url, enabled: settings.enable_app_store },
          { platform: 'android', url: settings.play_store_url, enabled: settings.enable_play_store },
        ]}
        onToggle={handleAppStoreToggle}
        onUpdate={handleAppStoreUrlChange}
      />

      {/* Mobile App Images */}
      <MobileAppImages
        image={{
          id: 'mobile-app',
          url: settings.mobile_app_image_url,
          alt: 'Mobile App Promo',
          enabled: true,
        }}
        onToggle={() => {}} // No toggle logic for single image
        onUpload={handleImageChange}
        onDelete={handleImageDelete}
      />

      {/* Markup & Offers Config */}
      <MarkupOffersConfig
        settings={{
          enableMarkupsOnList: settings.enable_markups,
          markupCalculationBase: settings.markup_calculation_basis,
        }}
        onToggleListDisplay={handleMarkupToggle}
        onBaseChange={handleMarkupBaseChange}
      />

      <button
        onClick={handleSave}
        disabled={saving || loading}
        className="px-6 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors disabled:opacity-50"
      >
        {saving ? 'Saving...' : 'Save Settings'}
      </button>
    </div>
  );
}
