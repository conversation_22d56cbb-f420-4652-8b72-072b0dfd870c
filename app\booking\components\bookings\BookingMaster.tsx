'use client';

import { useState, useEffect } from 'react';
import BookingStats from './BookingStats';
import BookingList from './BookingList';
import BookingAddEdit from './BookingAddEdit';
import BookingView from './BookingView';
import Modal from '../../../components/ui/Modal';
import { getAllBookingApi } from '../../booking.service';
import { Booking, BookingFormData, BookingFilters } from '../types';

export default function BookingMaster() {
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [selectedBooking, setSelectedBooking] = useState<Booking | null>(null);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [isViewOpen, setIsViewOpen] = useState(false);
  const [formMode, setFormMode] = useState<'add' | 'edit'>('add');
  const [loading, setLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(5);
  const [filters, setFilters] = useState<BookingFilters>({
    search: '',
    bookingStatus: '',
    paymentStatus: '',
    paymentMethod: '',
    dateRange: {
      startDate: '',
      endDate: ''
    },
    hotelId: '',
    bookingSource: ''
  });

  useEffect(() => {
    fetchBookings();
  }, []);

  const fetchBookings = async () => {
    setLoading(true);
    try {
      const fetchedBookings = await getAllBookingApi();
      setBookings(fetchedBookings);
    } catch (error) {
      console.log('Error fetching bookings:', error);
    }finally{
      setLoading(false);
    }
  };

  const handleAddBooking = () => {
    setSelectedBooking(null);
    setFormMode('add');
    setIsFormOpen(true);
  };

  const handleEditBooking = (booking: Booking) => {
    setSelectedBooking(booking);
    setFormMode('edit');
    setIsFormOpen(true);
  };

  const handleViewBooking = (booking: Booking) => {
    setSelectedBooking(booking);
    setIsViewOpen(true);
  };

  const handleFormSave = (bookingData: BookingFormData) => {
    if (formMode === 'add') {
      // Create new booking
      const newBooking: Booking = {
        id: Date.now().toString(),
        bookingId: `BK-2024-${String(bookings.length + 1).padStart(3, '0')}`,
        ...bookingData,
        numberOfNights: Math.ceil((new Date(bookingData.checkOutDate).getTime() - new Date(bookingData.checkInDate).getTime()) / (1000 * 60 * 60 * 24)),
        amountDue: bookingData.totalAmount - bookingData.amountPaid,
        bookingDate: new Date().toISOString(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        hotelName: 'Selected Hotel', // In real app, fetch from hotel data
        roomName: 'Selected Room' // In real app, fetch from room data
      };
      setBookings(prev => [newBooking, ...prev]);
    } else if (selectedBooking) {
      // Update existing booking
      const updatedBooking: Booking = {
        ...selectedBooking,
        ...bookingData,
        numberOfNights: Math.ceil((new Date(bookingData.checkOutDate).getTime() - new Date(bookingData.checkInDate).getTime()) / (1000 * 60 * 60 * 24)),
        amountDue: bookingData.totalAmount - bookingData.amountPaid,
        updatedAt: new Date().toISOString()
      };
      setBookings(prev => prev.map(booking => booking.id === selectedBooking.id ? updatedBooking : booking));
    }
    
    setIsFormOpen(false);
    setSelectedBooking(null);
  };

  const handleFormCancel = () => {
    setIsFormOpen(false);
    setSelectedBooking(null);
  };

  const handleViewClose = () => {
    setIsViewOpen(false);
    setSelectedBooking(null);
  };

  const handleViewEdit = (booking: Booking) => {
    setIsViewOpen(false);
    handleEditBooking(booking);
  };

  const handleDeleteBooking = (booking: Booking) => {
    if (window.confirm(`Are you sure you want to delete booking ${booking.bookingId}? This action cannot be undone.`)) {
      setBookings(prev => prev.filter(b => b.id !== booking.id));
    }
  };

  const filteredBookings = bookings.filter(booking => {
    if (filters.search && !booking.guestName.toLowerCase().includes(filters.search.toLowerCase()) && 
        !booking.bookingId.toLowerCase().includes(filters.search.toLowerCase())) {
      return false;
    }
    if (filters.bookingStatus && booking.bookingStatus !== filters.bookingStatus) {
      return false;
    }
    if (filters.paymentStatus && booking.paymentStatus !== filters.paymentStatus) {
      return false;
    }
    if (filters.paymentMethod && booking.paymentMethod !== filters.paymentMethod) {
      return false;
    }
    if (filters.hotelId && booking.hotelId !== filters.hotelId) {
      return false;
    }
    if (filters.bookingSource && booking.bookingSource !== filters.bookingSource) {
      return false;
    }
    return true;
  });

  // Pagination calculations
  const totalItems = filteredBookings.length;
  const totalPages = Math.ceil(totalItems / pageSize);
  const startIndex = (currentPage - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const paginatedBookings = filteredBookings.slice(startIndex, endIndex);

  // Reset to page 1 when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [filters]);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handlePageSizeChange = (newPageSize: number) => {
    setPageSize(newPageSize);
    setCurrentPage(1);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Statistics Cards */}
      <BookingStats />

      {/* Header with Actions */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Bookings</h2>
          <p className="text-gray-600">Manage all hotel bookings and reservations</p>
        </div>
        <div className="flex items-center space-x-3">
          <button
            onClick={handleAddBooking}
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors shadow-sm"
          >
            <i className="ri-add-line mr-2"></i>
            Add Booking
          </button>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 sm:p-6">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="relative">
            <i className="ri-search-line absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
            <input
              type="text"
              placeholder="Search bookings..."
              value={filters.search}
              onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
              className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
            />
          </div>

          <select
            value={filters.bookingStatus}
            onChange={(e) => setFilters(prev => ({ ...prev, bookingStatus: e.target.value as any }))}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm w-full sm:w-auto pr-8"
          >
            <option value="">All Booking Status</option>
            <option value="pending">Pending</option>
            <option value="confirmed">Confirmed</option>
            <option value="checked-in">Checked In</option>
            <option value="checked-out">Checked Out</option>
            <option value="completed">Completed</option>
            <option value="cancelled">Cancelled</option>
            <option value="no-show">No Show</option>
          </select>

          <select
            value={filters.paymentStatus}
            onChange={(e) => setFilters(prev => ({ ...prev, paymentStatus: e.target.value as any }))}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm w-full sm:w-auto pr-8"
          >
            <option value="">All Payment Status</option>
            <option value="pending">Pending</option>
            <option value="paid">Paid</option>
            <option value="partial">Partial</option>
            <option value="failed">Failed</option>
            <option value="refunded">Refunded</option>
            <option value="cancelled">Cancelled</option>
          </select>

          <select
            value={filters.paymentMethod}
            onChange={(e) => setFilters(prev => ({ ...prev, paymentMethod: e.target.value as any }))}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm w-full sm:w-auto pr-8"
          >
            <option value="">All Payment Methods</option>
            <option value="credit-card">Credit Card</option>
            <option value="debit-card">Debit Card</option>
            <option value="bank-transfer">Bank Transfer</option>
            <option value="cash">Cash</option>
            <option value="paypal">PayPal</option>
            <option value="stripe">Stripe</option>
            <option value="other">Other</option>
          </select>
        </div>
      </div>

      {/* Bookings List */}
      <BookingList
        bookings={paginatedBookings}
        onEdit={handleEditBooking}
        onView={handleViewBooking}
        onDelete={handleDeleteBooking}
        loading={false}
        currentPage={currentPage}
        totalPages={totalPages}
        pageSize={pageSize}
        totalItems={totalItems}
        onPageChange={handlePageChange}
        onPageSizeChange={handlePageSizeChange}
      />

      {/* Add/Edit Modal */}
      <Modal
        isOpen={isFormOpen}
        onClose={handleFormCancel}
        title={formMode === 'add' ? 'Add New Booking' : 'Edit Booking'}
        size="full"
        height="fixed"
      >
        <BookingAddEdit
          booking={selectedBooking}
          onSave={handleFormSave}
          onCancel={handleFormCancel}
          mode={formMode}
        />
      </Modal>

      {/* View Modal */}
      <BookingView
        booking={selectedBooking}
        isOpen={isViewOpen}
        onClose={handleViewClose}
        onEdit={handleViewEdit}
      />
    </div>
  );
}
