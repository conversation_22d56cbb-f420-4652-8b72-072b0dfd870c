'use client';

import React from 'react';
import TabbedModal from '@/app/components/ui/TabbedModal';
import { 
  VirtualGroup, 
  VirtualUtils
} from '../virtual.model';

interface ViewVirtualModalProps {
  virtual: VirtualGroup | null;
  onClose: () => void;
}

export default function ViewVirtualModal({ virtual, onClose }: ViewVirtualModalProps) {
  if (!virtual) return null;

  const getModelIcon = (modelName: string) => {
    const model = modelName?.toLowerCase() || '';
    if (model.includes('hotel')) return 'ri-hotel-line';
    if (model.includes('room')) return 'ri-door-line';
    if (model.includes('product')) return 'ri-product-hunt-line';
    return 'ri-folder-3-line';
  };

  const getModelColor = (modelName: string) => {
    const model = modelName?.toLowerCase() || '';
    if (model.includes('hotel')) return 'text-blue-600 bg-blue-100';
    if (model.includes('room')) return 'text-green-600 bg-green-100';
    if (model.includes('product')) return 'text-purple-600 bg-purple-100';
    return 'text-gray-600 bg-gray-100';
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const tabs = [
    {
      id: 'overview',
      label: 'Overview',
      icon: 'ri-information-line',
      content: (
        <div className="p-6 space-y-6">
          {/* Group Header */}
          <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl p-6 border border-blue-100">
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center space-x-4">
                <div className={`w-16 h-16 rounded-2xl flex items-center justify-center ${getModelColor(virtual.model_name)}`}>
                  <i className={`${getModelIcon(virtual.model_name)} text-2xl`}></i>
                </div>
                <div>
                  <h3 className="text-xl font-bold text-gray-900">{virtual.model_name}</h3>
                  <p className="text-gray-600 mt-1">ID: {virtual.id} • {virtual.is_function ? 'Function-based' : 'Field-based'} Rule</p>
                </div>
              </div>
              <span className="px-3 py-1 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800 border border-indigo-200">
                {virtual.has_child ? 'Has Child' : 'No Child'}
              </span>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="bg-white rounded-lg p-4 border border-blue-200">
                <div className="text-sm text-gray-600">Rule Type</div>
                <div className="font-semibold text-gray-900">{virtual.is_function ? 'Function' : 'Field'}</div>
              </div>
              <div className="bg-white rounded-lg p-4 border border-blue-200">
                <div className="text-sm text-gray-600">Parent</div>
                <div className="font-semibold text-gray-900">{virtual.parent_id || 'Top-Level'}</div>
              </div>
              <div className="bg-white rounded-lg p-4 border border-blue-200">
                <div className="text-sm text-gray-600">Markup Connections</div>
                <div className="font-semibold text-gray-900">{virtual.markup_connections?.length || 0}</div>
              </div>
              <div className="bg-white rounded-lg p-4 border border-blue-200">
                <div className="text-sm text-gray-600">Offer Connections</div>
                <div className="font-semibold text-gray-900">{virtual.offer_connections?.length || 0}</div>
              </div>
            </div>
          </div>

          {/* Rule Description */}
          <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
            <div className="flex items-center mb-4">
              <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                <i className="ri-code-line text-purple-600 text-lg"></i>
              </div>
              <h3 className="text-lg font-semibold text-gray-900">Rule Description</h3>
            </div>
            <div className="bg-purple-50 rounded-lg p-4 border border-purple-200">
              <code className="text-sm text-purple-900 font-mono">
                {VirtualUtils.getRuleDescription(virtual)}
              </code>
            </div>
          </div>

          {/* Timeline */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
              <div className="flex items-center mb-4">
                <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                  <i className="ri-time-line text-green-600 text-lg"></i>
                </div>
                <h3 className="text-lg font-semibold text-gray-900">Timeline</h3>
              </div>
              <div className="space-y-4">
                <div className="flex justify-between items-center py-2 border-b border-gray-100">
                  <span className="text-sm text-gray-600">Created</span>
                  <span className="text-sm font-medium text-gray-900">{formatDate(virtual.created_at)}</span>
                </div>
                <div className="flex justify-between items-center py-2">
                  <span className="text-sm text-gray-600">Last Updated</span>
                  <span className="text-sm font-medium text-gray-900">{formatDate(virtual.updated_at)}</span>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
              <div className="flex items-center mb-4">
                <div className="w-10 h-10 bg-amber-100 rounded-lg flex items-center justify-center mr-3">
                  <i className="ri-database-line text-amber-600 text-lg"></i>
                </div>
                <h3 className="text-lg font-semibold text-gray-900">Model Information</h3>
              </div>
              <div className="space-y-4">
                <div className="flex justify-between items-center py-2 border-b border-gray-100">
                  <span className="text-sm text-gray-600">Model Name</span>
                  <div className="text-sm font-medium text-gray-900">{virtual.model_name}</div>
                </div>
                <div className="flex justify-between items-center py-2">
                  <span className="text-sm text-gray-600">Group ID</span>
                  <div className="text-sm font-medium text-gray-900">{virtual.id}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )
    },
    {
      id: 'details',
      label: 'Rule Details',
      icon: 'ri-settings-line',
      content: (
        <div className="p-6 space-y-6">
          {/* Rule Configuration */}
          <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
            <div className="flex items-center mb-4">
              <div className="w-10 h-10 bg-indigo-100 rounded-lg flex items-center justify-center mr-3">
                <i className="ri-settings-3-line text-indigo-600 text-lg"></i>
              </div>
              <h3 className="text-lg font-semibold text-gray-900">Rule Configuration</h3>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div className="flex justify-between items-center py-3 border-b border-gray-100">
                  <span className="text-sm font-medium text-gray-600">Field</span>
                  <span className="text-sm text-gray-900 font-mono">{virtual.field || 'N/A'}</span>
                </div>
                <div className="flex justify-between items-center py-3 border-b border-gray-100">
                  <span className="text-sm font-medium text-gray-600">Function</span>
                  <span className="text-sm text-gray-900 font-mono">{virtual.function || 'N/A'}</span>
                </div>
              </div>
              <div className="space-y-4">
                <div className="flex justify-between items-center py-3 border-b border-gray-100">
                  <span className="text-sm font-medium text-gray-600">Operator</span>
                  <span className="text-sm text-gray-900 font-mono">{virtual.operator || 'N/A'}</span>
                </div>
                <div className="flex justify-between items-center py-3 border-b border-gray-100">
                  <span className="text-sm font-medium text-gray-600">Value</span>
                  <span className="text-sm text-gray-900 font-mono">{virtual.value || 'N/A'}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Child Rules */}
          {virtual.child && (
            <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
              <div className="flex items-center mb-4">
                <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                  <i className="ri-node-tree text-blue-600 text-lg"></i>
                </div>
                <h3 className="text-lg font-semibold text-gray-900">Child Rule</h3>
              </div>
              <div className="bg-blue-50 rounded-lg p-4 border border-blue-200">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs font-medium">
                        ID: {virtual.child.id}
                      </span>
                      <span className="text-sm text-gray-600">{virtual.child.model_name}</span>
                    </div>
                    <code className="text-sm text-blue-900 font-mono">
                      {VirtualUtils.getRuleDescription(virtual.child)}
                    </code>
                  </div>
                </div>
              </div>
              {/* Nested child if exists */}
              {virtual.child.child && (
                <div className="mt-3 ml-6">
                  <div className="bg-indigo-50 rounded-lg p-4 border border-indigo-200">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-2">
                          <span className="px-2 py-1 bg-indigo-100 text-indigo-800 rounded text-xs font-medium">
                            Nested Child ID: {virtual.child.child.id}
                          </span>
                          <span className="text-sm text-gray-600">{virtual.child.child.model_name}</span>
                        </div>
                        <code className="text-sm text-indigo-900 font-mono">
                          {VirtualUtils.getRuleDescription(virtual.child.child)}
                        </code>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Connections */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Markup Connections */}
            {virtual.markup_connections && virtual.markup_connections.length > 0 && (
              <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
                <div className="flex items-center mb-4">
                  <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                    <i className="ri-price-tag-3-line text-green-600 text-lg"></i>
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900">Markup Connections</h3>
                </div>
                <div className="space-y-2">
                  {virtual.markup_connections.map(conn => (
                    <div key={conn.id} className="bg-green-50 rounded-lg p-3 border border-green-200">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">Markup ID</span>
                        <span className="text-sm font-medium text-gray-900">{conn.markup_id}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Offer Connections */}
            {virtual.offer_connections && virtual.offer_connections.length > 0 && (
              <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
                <div className="flex items-center mb-4">
                  <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center mr-3">
                    <i className="ri-gift-line text-orange-600 text-lg"></i>
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900">Offer Connections</h3>
                </div>
                <div className="space-y-2">
                  {virtual.offer_connections.map(conn => (
                    <div key={conn.id} className="bg-orange-50 rounded-lg p-3 border border-orange-200">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">Offer ID</span>
                        <span className="text-sm font-medium text-gray-900">{conn.offer_id}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      )
    }
  ];

  return (
    <TabbedModal
      isOpen={!!virtual}
      onClose={onClose}
      title={virtual.model_name}
      subtitle={`Virtual Group ID: ${virtual.id} • ${VirtualUtils.getRuleDescription(virtual)}`}
      tabs={tabs}
      defaultTab="overview"
      size="full"
      height="fixed"
      headerActions={
        <div className="flex items-center space-x-2">
          <span className={`inline-flex items-center px-3 py-2 rounded-lg text-sm font-medium ${getModelColor(virtual.model_name)}`}>
            <i className={`${getModelIcon(virtual.model_name)} mr-2`}></i>
            {virtual.model_name}
          </span>
        </div>
      }
    />
  );
}