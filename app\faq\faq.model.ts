// FAQ Models and Types

export interface Faq {
  ID: number;
  CreatedAt: string;
  UpdatedAt: string;
  DeletedAt: string | null;
  question: string;
  answer: string;
  service: string;
  page_type: string;
  service_id: number;
  language: string;
  language_code: string;
}

export interface FaqResponse {
  success: boolean;
  data: Faq | Faq[];
  message?: string;
  total?: number;
}

export interface CreateFaqRequest {
  service_id: number;
  question: string;
  answer: string;
  service: string;
  page_type: string;
  language: string;
  language_code: string;
}

export interface UpdateFaqRequest {
  service_id?: number;
  question?: string;
  answer?: string;
  service?: string;
  page_type?: string;
  language?: string;
  language_code?: string;
}

export interface FaqFormData {
  service_id: number;
  question: string;
  answer: string;
  service: string;
  page_type: string;
  language: string;
  language_code: string;
}

export interface FaqFilters {
  search: string;
  language: string;
  language_code: string;
  service: string;
  page_type: string;
  dateRange: {
    startDate: string;
    endDate: string;
  };
}

// Form validation errors
export interface FaqFormErrors {
  service_id?: string;
  question?: string;
  answer?: string;
  service?: string;
  page_type?: string;
  language?: string;
  language_code?: string;
  submit?: string;
}

// API response for single FAQ
export interface SingleFaqResponse {
  success: boolean;
  data: Faq;
  message?: string;
}

// API response for multiple FAQs
export interface MultipleFaqResponse {
  success: boolean;
  data: Faq[];
  total: number;
  message?: string;
}

// API response for FAQs by language
export interface FaqByLanguageResponse {
  success: boolean;
  data: {
    data: Faq[];
    total: number;
    page: number;
    page_size: number;
    total_pages: number;
  };
  message?: string;
}

// Language options for dropdown
export interface LanguageOption {
  label: string;
  value: string;
  code: string;
}

export const LANGUAGE_OPTIONS: LanguageOption[] = [
  { label: 'English', value: 'English', code: 'en' },
  { label: 'Hindi', value: 'Hindi', code: 'hi' },
  { label: 'Malayalam', value: 'malayalam', code: 'ml' },
  { label: 'French', value: 'French', code: 'fr' },
  { label: 'German', value: 'German', code: 'de' },
  { label: 'Italian', value: 'Italian', code: 'it' },
  { label: 'Portuguese', value: 'Portuguese', code: 'pt' },
  { label: 'Spanish', value: 'Spanish', code: 'es' },
  { label: 'Chinese', value: 'Chinese', code: 'zh' },
  { label: 'Japanese', value: 'Japanese', code: 'ja' },
  { label: 'Korean', value: 'Korean', code: 'ko' },
  { label: 'Arabic', value: 'Arabic', code: 'ar' },
  { label: 'Russian', value: 'Russian', code: 'ru' }
];

// Service options
export const SERVICE_OPTIONS = [
  { label: 'Hotel', value: 'hotel' },
  { label: 'Flight', value: 'flight' },
  { label: 'Train', value: 'train' },
  { label: 'Bus', value: 'bus' },
  { label: 'Car Rental', value: 'car' },
  { label: 'Package', value: 'package' }
];

// Page type options
export const PAGE_TYPE_OPTIONS = [
  { label: 'Booking', value: 'booking' },
  { label: 'Payment', value: 'payment' },
  { label: 'Cancellation', value: 'cancellation' },
  { label: 'Support', value: 'support' },
  { label: 'General', value: 'general' }
];

// Utility functions
export const getLanguageByCode = (code: string): LanguageOption | undefined => {
  return LANGUAGE_OPTIONS.find(lang => lang.code === code);
};

export const getLanguageByName = (name: string): LanguageOption | undefined => {
  return LANGUAGE_OPTIONS.find(lang => lang.value === name);
};

export const getServiceOption = (value: string) => {
  return SERVICE_OPTIONS.find(option => option.value === value);
};

export const getPageTypeOption = (value: string) => {
  return PAGE_TYPE_OPTIONS.find(option => option.value === value);
};

// Default form data
export const getDefaultFaqFormData = (): FaqFormData => ({
  service_id: 1,
  question: '',
  answer: '',
  service: '',
  page_type: '',
  language: '',
  language_code: '',
});

// Form validation
export const validateFaqForm = (formData: FaqFormData): FaqFormErrors => {
  const errors: FaqFormErrors = {};

  if (!formData.question.trim()) {
    errors.question = 'Question is required';
  } else if (formData.question.trim().length < 10) {
    errors.question = 'Question must be at least 10 characters long';
  }

  if (!formData.answer.trim()) {
    errors.answer = 'Answer is required';
  } else if (formData.answer.trim().length < 10) {
    errors.answer = 'Answer must be at least 10 characters long';
  }

  if (!formData.service.trim()) {
    errors.service = 'Service is required';
  }

  if (!formData.page_type.trim()) {
    errors.page_type = 'Page type is required';
  }

  if (!formData.language.trim()) {
    errors.language = 'Language is required';
  }

  if (!formData.language_code.trim()) {
    errors.language_code = 'Language code is required';
  }

  if (!formData.service_id || formData.service_id <= 0) {
    errors.service_id = 'Service ID must be a positive number';
  }

  return errors;
};

// Date formatting utility
export const formatDate = (dateString: string): string => {
  try {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  } catch (error) {
    return dateString;
  }
};

// Default filters
export const getDefaultFaqFilters = (): FaqFilters => ({
  search: '',
  language: '',
  language_code: '',
  service: '',
  page_type: '',
  dateRange: {
    startDate: '',
    endDate: '',
  },
});

// FAQ Statistics interface
export interface FaqStats {
  totalFaqs: number;
  totalLanguages: number;
  totalServices: number;
  lastUpdated: string;
}
