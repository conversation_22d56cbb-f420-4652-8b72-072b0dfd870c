'use client';

import { useState, useEffect } from 'react';
import { 
  Coupon, 
  CouponFormData, 
  COUPON_TYPE_OPTIONS, 
  BUSINESS_TYPE_OPTIONS, 
  SERVICE_TYPE_OPTIONS,
  CouponUtils 
} from '../coupon.model';
import CouponService from '../coupon.service';

interface CouponAddEditProps {
  coupon: Coupon | null;
  onSave: () => void;
  onCancel: () => void;
  mode?: 'add' | 'edit';
}

export default function CouponAddEdit({ coupon, onSave, onCancel, mode = 'add' }: CouponAddEditProps) {
  const [formData, setFormData] = useState<CouponFormData>({
    name: '',
    coupon_type: 'PERCENTAGE',
    coupon_value: 0,
    business_type: 'B2C',
    service_type: 'HOTEL',
    coupon_code: '',
    description: '',
    user_id: null,
    image: null,
    terms_and_conditions: null,
    valid_from: null,
    valid_to: null
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [loading, setLoading] = useState(false);
  const [isGeneratingCode, setIsGeneratingCode] = useState(false);
  const [uploadedImage, setUploadedImage] = useState<File | null>(null);
  const [imagePreviewUrl, setImagePreviewUrl] = useState<string | null>(null);

  useEffect(() => {
    if (coupon) {
      setFormData({
        name: coupon.name,
        coupon_type: coupon.coupon_type,
        coupon_value: coupon.coupon_value,
        business_type: coupon.business_type,
        service_type: coupon.service_type,
        coupon_code: coupon.coupon_code,
        description: coupon.description,
        user_id: coupon.user_id,
        image: coupon.image,
        terms_and_conditions: coupon.terms_and_conditions,
        valid_from: coupon.valid_from ? coupon.valid_from.split('T')[0] : null,
        valid_to: coupon.valid_to ? coupon.valid_to.split('T')[0] : null
      });

      if (coupon.image) {
        setImagePreviewUrl(coupon.image);
      }
    }
  }, [coupon]);

  const handleInputChange = (field: keyof CouponFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  // Handle image upload similar to hotel management
  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setUploadedImage(file);

      // Create preview URL
      const reader = new FileReader();
      reader.onload = (e) => {
        if (e.target?.result) {
          setImagePreviewUrl(e.target.result as string);
        }
      };
      reader.readAsDataURL(file);
    }
  };

  // Handle image removal
  const handleImageRemove = () => {
    setUploadedImage(null);
    setImagePreviewUrl(null);
    setFormData(prev => ({ ...prev, image: null }));
  };



  const generateCouponCode = () => {
    setIsGeneratingCode(true);
    const prefix = formData.name.substring(0, 3).toUpperCase().replace(/[^A-Z]/g, '');
    const newCode = CouponUtils.generateCouponCode(prefix);
    setFormData(prev => ({ ...prev, coupon_code: newCode }));
    setIsGeneratingCode(false);
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Coupon name is required';
    }

    if (!formData.coupon_code.trim()) {
      newErrors.coupon_code = 'Coupon code is required';
    } else if (!CouponUtils.isValidCouponCode(formData.coupon_code)) {
      newErrors.coupon_code = 'Coupon code must be 4-12 characters, letters and numbers only';
    }

    if (formData.coupon_value <= 0) {
      newErrors.coupon_value = 'Coupon value must be greater than 0';
    }

    if (formData.coupon_type === 'PERCENTAGE' && formData.coupon_value > 100) {
      newErrors.coupon_value = 'Percentage discount cannot exceed 100%';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Description is required';
    }

    if (formData.valid_from && formData.valid_to) {
      const fromDate = new Date(formData.valid_from);
      const toDate = new Date(formData.valid_to);
      
      if (fromDate >= toDate) {
        newErrors.valid_to = 'End date must be after start date';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      // Use the image URL from form data (file upload would need a separate endpoint)
      const submitData = {
        ...formData,
        image: formData.image || imagePreviewUrl,
        valid_from: formData.valid_from ? `${formData.valid_from}T00:00:00Z` : null,
        valid_to: formData.valid_to ? `${formData.valid_to}T23:59:59Z` : null
      };

      if (coupon) {
        await CouponService.updateCoupon(coupon.id, submitData);
      } else {
        await CouponService.createCoupon(submitData);
      }
      
      onSave();
    } catch (error: any) {
      setErrors({ submit: error.message || 'An error occurred while saving the coupon' });
    } finally {
      setLoading(false);
    }
  };

  const inputStyle = "block w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm";
  const errorInputStyle = "border-red-300 bg-red-50";

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {errors.submit && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex">
            <i className="ri-error-warning-line text-red-400 mr-2"></i>
            <p className="text-sm text-red-700">{errors.submit}</p>
          </div>
        </div>
      )}

      {/* Basic Information */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Coupon Name *
          </label>
          <input
            type="text"
            value={formData.name}
            onChange={(e) => handleInputChange('name', e.target.value)}
            placeholder="Enter coupon name"
            className={`${inputStyle} ${errors.name ? errorInputStyle : 'border-gray-300'}`}
          />
          {errors.name && <p className="text-red-500 text-xs mt-1">{errors.name}</p>}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Coupon Code *
          </label>
          <div className="flex">
            <input
              type="text"
              value={formData.coupon_code}
              onChange={(e) => handleInputChange('coupon_code', e.target.value.toUpperCase())}
              placeholder="COUPON2025"
              className={`${inputStyle} rounded-r-none ${errors.coupon_code ? errorInputStyle : 'border-gray-300'}`}
            />
            <button
              type="button"
              onClick={generateCouponCode}
              disabled={isGeneratingCode}
              className="px-3 py-2 bg-gray-100 border border-l-0 border-gray-300 rounded-r-lg hover:bg-gray-200 text-sm"
            >
              {isGeneratingCode ? (
                <i className="ri-loader-4-line animate-spin"></i>
              ) : (
                <i className="ri-refresh-line"></i>
              )}
            </button>
          </div>
          {errors.coupon_code && <p className="text-red-500 text-xs mt-1">{errors.coupon_code}</p>}
        </div>
      </div>

      {/* Coupon Type and Value */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Coupon Type *
          </label>
          <select
            value={formData.coupon_type}
            onChange={(e) => handleInputChange('coupon_type', e.target.value)}
            className={`${inputStyle} border-gray-300`}
          >
            {COUPON_TYPE_OPTIONS.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Coupon Value *
          </label>
          <div className="relative">
            <input
              type="number"
              value={formData.coupon_value}
              onChange={(e) => handleInputChange('coupon_value', parseFloat(e.target.value) )}
              placeholder="0"
              min="0"
              step={formData.coupon_type === 'PERCENTAGE' ? '1' : '0.01'}
              max={formData.coupon_type === 'PERCENTAGE' ? '100' : undefined}
              className={`${inputStyle} ${errors.coupon_value ? errorInputStyle : 'border-gray-300'}`}
            />
            <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
              <span className="text-gray-500 text-sm">
                {formData.coupon_type === 'PERCENTAGE' ? '%' : '$'}
              </span>
            </div>
          </div>
          {errors.coupon_value && <p className="text-red-500 text-xs mt-1">{errors.coupon_value}</p>}
        </div>
      </div>

      {/* Business and Service Type */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Business Type *
          </label>
          <select
            value={formData.business_type}
            onChange={(e) => handleInputChange('business_type', e.target.value)}
            className={`${inputStyle} border-gray-300`}
          >
            {BUSINESS_TYPE_OPTIONS.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Service Type *
          </label>
          <select
            value={formData.service_type}
            onChange={(e) => handleInputChange('service_type', e.target.value)}
            className={`${inputStyle} border-gray-300`}
          >
            {SERVICE_TYPE_OPTIONS.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Description */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Description *
        </label>
        <textarea
          value={formData.description}
          onChange={(e) => handleInputChange('description', e.target.value)}
          placeholder="Describe the coupon offer..."
          rows={3}
          className={`${inputStyle} ${errors.description ? errorInputStyle : 'border-gray-300'}`}
        />
        {errors.description && <p className="text-red-500 text-xs mt-1">{errors.description}</p>}
      </div>

      {/* Terms and Conditions */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Terms and Conditions
        </label>
        <textarea
          value={formData.terms_and_conditions || ''}
          onChange={(e) => handleInputChange('terms_and_conditions', e.target.value)}
          placeholder="Enter terms and conditions..."
          rows={3}
          className={`${inputStyle} border-gray-300`}
        />
      </div>

      {/* Validity Dates */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Valid From
          </label>
          <input
            type="date"
            value={formData.valid_from || ''}
            onChange={(e) => handleInputChange('valid_from', e.target.value)}
            className={`${inputStyle} border-gray-300`}
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Valid To
          </label>
          <input
            type="date"
            value={formData.valid_to || ''}
            onChange={(e) => handleInputChange('valid_to', e.target.value)}
            className={`${inputStyle} ${errors.valid_to ? errorInputStyle : 'border-gray-300'}`}
          />
          {errors.valid_to && <p className="text-red-500 text-xs mt-1">{errors.valid_to}</p>}
        </div>
      </div>

      {/* Image Upload - Hotel Management Style */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Coupon Image
        </label>
        <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors">
          <input
            type="file"
            accept="image/*"
            onChange={handleImageUpload}
            className="hidden"
            id="image-upload"
          />
          <label htmlFor="image-upload" className="cursor-pointer">
            <i className="ri-upload-cloud-2-line text-4xl text-gray-400 mb-2 block"></i>
            <p className="text-sm text-gray-600">
              Click to upload image or drag and drop
            </p>
            <p className="text-xs text-gray-500 mt-1">
              PNG, JPG, GIF up to 10MB
            </p>
          </label>
        </div>

        {/* Image Preview */}
        {(imagePreviewUrl || formData.image) && (
          <div className="mt-4">
            <h4 className="text-sm font-medium text-gray-700 mb-3">Image Preview</h4>
            <div className="relative group inline-block">
              <img
                src={imagePreviewUrl || formData.image || ''}
                alt="Coupon preview"
                className="w-32 h-32 object-cover rounded-lg border border-gray-200"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.src = `https://via.placeholder.com/128x128/f3f4f6/6b7280?text=Image+Error`;
                }}
              />
              <button
                type="button"
                onClick={handleImageRemove}
                className="absolute top-2 right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity hover:bg-red-600"
              >
                <i className="ri-close-line text-xs"></i>
              </button>
            </div>
          </div>
        )}

        {/* URL Input as Alternative */}
        <div className="mt-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Or enter image URL
          </label>
          <input
            type="url"
            value={formData.image || ''}
            onChange={(e) => {
              handleInputChange('image', e.target.value);
              if (e.target.value) {
                setImagePreviewUrl(e.target.value);
                setUploadedImage(null);
              }
            }}
            placeholder="https://example.com/coupon-image.jpg"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
          />
        </div>

        {errors.image && (
          <p className="text-red-500 text-sm mt-1">{errors.image}</p>
        )}
      </div>

      {/* User ID (Optional) */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          User ID (Optional)
        </label>
        <input
          type="number"
          value={formData.user_id || ''}
          onChange={(e) => handleInputChange('user_id', e.target.value ? parseInt(e.target.value) : null)}
          placeholder="Enter user ID for user-specific coupon"
          className={`${inputStyle} border-gray-300`}
        />
        <p className="text-xs text-gray-500 mt-1">Leave empty for general coupons</p>
      </div>

      {/* Form Actions */}
      <div className="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          Cancel
        </button>
        <button
          type="submit"
          disabled={loading}
          className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {loading ? (
            <>
              <i className="ri-loader-4-line animate-spin mr-2"></i>
              {mode === 'edit' ? 'Updating...' : 'Creating...'}
            </>
          ) : (
            <>
              <i className="ri-save-line mr-2"></i>
              {mode === 'edit' ? 'Update Coupon' : 'Create Coupon'}
            </>
          )}
        </button>
      </div>
    </form>
  );
}
