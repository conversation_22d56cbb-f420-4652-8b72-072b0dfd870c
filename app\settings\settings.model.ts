



export interface SettingsResponse {
 app_store_url: string;
 enable_app_store: boolean;
 play_store_url: string;
 enable_play_store: boolean;
 mobile_app_image_url: string;
 enable_markups: boolean;
 enable_offers: boolean;
 markup_calculation_basis: 'base' | 'total'; // Corrected
}

export interface UpdateSettingsRequest {
 app_store_url?: string;
 enable_app_store?: boolean;
 play_store_url?: string;
 enable_play_store?: boolean;
 mobile_app_image?: File | null; // for upload
 enable_markups?: boolean;
 enable_offers?: boolean;
 markup_calculation_basis?: 'base' | 'total'; // Corrected
}