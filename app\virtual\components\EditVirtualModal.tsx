'use client';

import React, { useEffect, useState } from 'react';
import {
  VirtualGroup,
  UpdatedVirtualFormData,
  WithCondition,
  CreateVirtualGroupRequest,
  UpdateVirtualGroupRequest,
  MODEL_TYPE_OPTIONS,
  FUNCTION_OPTIONS,
  FIELD_OPTIONS,
  OPERATION_OPTIONS,
  VirtualUtils,
} from '../virtual.model';
import virtualService from '../virtual.service';
import Modal from '../../components/ui/Modal';

// --- UI-Specific Constants (Same as VirtualSearchForm) ---
const MODE_TYPE_OPTIONS = [
  { value: 'all', label: 'All' },
  { value: 'having', label: 'Having' },
  { value: 'with', label: 'With' },
] as const;

const FUNCTION_TYPE_OPTIONS = [
  { value: 'Function', label: 'Function' },
  { value: 'Field', label: 'Field' },
] as const;

// --- Nested API Request Type ---
type NestedCreateVirtualGroupRequest = {
  model_name: string;
  is_function: boolean;
  operator: string;
  value: string;
  function: string | null;
  field: string | null;
  has_child?: boolean;
  child?: NestedCreateVirtualGroupRequest;
};

// --- Reusable UI Components (Exact same as VirtualSearchForm) ---

const HavingFields = ({ data, onChange, isNested = false }: { data: any, onChange: (field: any, value: any) => void, isNested?: boolean }) => (
  <div className={`rounded-lg p-6 border ${isNested ? 'bg-white mt-4' : 'bg-blue-50 border-blue-200'}`}>
    <h3 className="text-md font-semibold text-gray-800 mb-4 flex items-center">
      <i className="ri-function-line mr-2 text-blue-600"></i>
      Having Configuration
    </h3>
    <div className="space-y-4">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">Function Type *</label>
        <select value={data.functionType || ''} onChange={(e) => onChange('functionType', e.target.value)} className="w-full px-3 py-2 border border-gray-300 rounded-lg" required>
          <option value="">Select Type</option>
          {FUNCTION_TYPE_OPTIONS.map(o => <option key={o.value} value={o.value}>{o.label}</option>)}
        </select>
      </div>
      {data.functionType === 'Function' && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Function *</label>
            <select value={data.function || ''} onChange={(e) => onChange('function', e.target.value)} className="w-full px-3 py-2 border border-gray-300 rounded-lg" required>
              <option value="">Select Function</option>
              {FUNCTION_OPTIONS.map(o => <option key={o.value} value={o.value}>{o.label}</option>)}
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Field</label>
            <select value={data.field || ''} onChange={(e) => onChange('field', e.target.value)} className="w-full px-3 py-2 border border-gray-300 rounded-lg">
              <option value="">Select Field (Optional)</option>
              {FIELD_OPTIONS.map(o => <option key={o.value} value={o.value}>{o.label}</option>)}
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Operation *</label>
            <select value={data.operation || ''} onChange={(e) => onChange('operation', e.target.value)} className="w-full px-3 py-2 border border-gray-300 rounded-lg" required>
              <option value="">Select Operation</option>
              {OPERATION_OPTIONS.map(o => <option key={o.value} value={o.value}>{o.label}</option>)}
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Value *</label>
            <input type="text" value={data.value || ''} onChange={(e) => onChange('value', e.target.value)} className="w-full px-3 py-2 border border-gray-300 rounded-lg" placeholder="Enter value" required />
          </div>
        </div>
      )}
      {data.functionType === 'Field' && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Field *</label>
            <select value={data.field || ''} onChange={(e) => onChange('field', e.target.value)} className="w-full px-3 py-2 border border-gray-300 rounded-lg" required>
              <option value="">Select Field</option>
              {FIELD_OPTIONS.map(o => <option key={o.value} value={o.value}>{o.label}</option>)}
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Operation *</label>
            <select value={data.operation || ''} onChange={(e) => onChange('operation', e.target.value)} className="w-full px-3 py-2 border border-gray-300 rounded-lg" required>
              <option value="">Select Operation</option>
              {OPERATION_OPTIONS.map(o => <option key={o.value} value={o.value}>{o.label}</option>)}
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Value *</label>
            <input type="text" value={data.value || ''} onChange={(e) => onChange('value', e.target.value)} className="w-full px-3 py-2 border border-gray-300 rounded-lg" placeholder="Enter value" required />
          </div>
        </div>
      )}
    </div>
  </div>
);

const WithConditionGroup = ({ condition, onChange }: { condition: WithCondition, onChange: (id: number, field: keyof WithCondition, value: any) => void }) => {
  return (
    <div className="bg-white p-4 rounded-md border border-gray-200 mb-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Model *</label>
          <select value={condition.model} onChange={(e) => onChange(condition.id, 'model', e.target.value)} className="w-full px-3 py-2 border border-gray-300 rounded-lg" required>
            {MODEL_TYPE_OPTIONS.map(o => <option key={o.value} value={o.value}>{o.label}</option>)}
          </select>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Mode *</label>
          <select value={condition.modeType} onChange={(e) => onChange(condition.id, 'modeType', e.target.value)} className="w-full px-3 py-2 border border-gray-300 rounded-lg" required>
            {MODE_TYPE_OPTIONS.map(o => <option key={o.value} value={o.value}>{o.label}</option>)}
          </select>
        </div>
      </div>
      
      {/* Only show Having Configuration when mode is 'having' - not for 'with' mode */}
      {condition.modeType === 'having' && (
        <HavingFields data={condition} onChange={(field, value) => onChange(condition.id, field, value)} isNested={true} />
      )}
      
      {/* If mode is 'with', show nested conditions */}
      {condition.modeType === 'with' && condition.withConditions && condition.withConditions.length > 0 && (
        <div className="mt-4 pl-4 border-l-2 border-gray-200">
          <h4 className="text-sm font-semibold text-gray-600 mb-2">Nested Conditions</h4>
          {condition.withConditions.map(nested => <WithConditionGroup key={nested.id} condition={nested} onChange={onChange} />)}
        </div>
      )}
    </div>
  );
};

interface EditVirtualModalProps {
  virtual: VirtualGroup | null;
  allGroups: VirtualGroup[];
  isOpen: boolean;
  onSave: (virtual: VirtualGroup) => void;
  onClose: () => void;
}

export default function EditVirtualModal({
  virtual,
  isOpen,
  onSave,
  onClose,
}: EditVirtualModalProps) {
  const initialForm: UpdatedVirtualFormData = {
    name: '',
    model: 'Hotel',
    modeType: 'all',
    status: 'ACTIVE',
    withConditions: [],
  };

  const [formData, setFormData] = useState<UpdatedVirtualFormData>(initialForm);
  const [submitting, setSubmitting] = useState(false);

  // Convert a WithCondition (UI) into the nested API request shape:
  // Matches new simplified API structure (no is_function, function, or parent_id)
  const transformToNestedRequest = (cond: WithCondition): CreateVirtualGroupRequest => {
    // Build the basic request object
    const apiData: CreateVirtualGroupRequest = {
      model_name: cond.model,
      field: cond.field || '',      // Empty string for 'with' mode
      operator: cond.operation || '',
      value: cond.value || '',
    };

    // If mode is 'with', we have nested conditions
    if (cond.modeType === 'with' && cond.withConditions && cond.withConditions.length > 0) {
      const childCond = cond.withConditions[0];
      apiData.has_child = true;
      apiData.child = transformToNestedRequest(childCond);
    }
    // If mode is 'having' (leaf node), don't set has_child or child properties

    return apiData;
  };

  // When a VirtualGroup is provided, transform it into the UpdatedVirtualFormData shape used by UI
  useEffect(() => {
    if (virtual) {
      const transformed = VirtualUtils.transformApiToFormData(virtual);
      setFormData(transformed);
    } else {
      setFormData(initialForm);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [virtual]);

  const handleInputChange = (field: keyof UpdatedVirtualFormData, value: any) => {
    setFormData((prev) => {
      const next = { ...prev } as UpdatedVirtualFormData;

      // Generic set
      (next as any)[field] = value;

      // Special handling when changing modeType
      if (field === 'modeType') {
        if (value !== 'having') {
          delete (next as any).functionType;
          delete (next as any).function;
          delete (next as any).field;
          delete (next as any).operation;
          delete (next as any).value;
        } else {
          next.functionType = 'Field';
          next.field = 'city';
          next.operation = '=';
          next.value = '';
        }

        if (value === 'with') {
          if (!next.withConditions || next.withConditions.length === 0) {
            next.withConditions = [
              {
                id: Date.now(),
                model: next.model || 'Hotel',
                modeType: 'having',
                functionType: 'Field',
                field: 'city',
                operation: '=',
                value: '',
              },
            ];
          }
        } else {
          next.withConditions = [];
        }
      }

      // If switching function/field type in having mode
      if (field === 'functionType' && prev.modeType === 'having') {
        if (value === 'Function') {
          delete (next as any).field;
          next.function = 'COUNT';
        } else {
          delete (next as any).function;
          next.field = 'city';
        }
        next.operation = '=';
        next.value = '';
      }

      return next;
    });
  };

  const handleWithConditionChange = (id: number, field: keyof WithCondition, value: any) => {
    const updateRecursively = (conds: WithCondition[] = []): WithCondition[] => {
      return conds.map((c) => {
        if (c.id === id) {
          const updated: WithCondition = { ...c, [field]: value };
          if (field === 'modeType') {
            delete (updated as any).functionType;
            delete (updated as any).function;
            delete (updated as any).field;
            delete (updated as any).operation;
            delete (updated as any).value;
            if (value === 'with') {
              updated.withConditions = [
                {
                  id: Date.now(),
                  model: 'Hotel',
                  modeType: 'having',
                  functionType: 'Field',
                  field: 'Category',
                  operation: '=',
                  value: '',
                },
              ];
            } else {
              delete updated.withConditions;
            }
          }
          if (field === 'functionType') {
            delete (updated as any).function;
            delete (updated as any).field;
            delete (updated as any).operation;
            delete (updated as any).value;
            if (value === 'Function') updated.function = 'COUNT';
            else updated.field = 'Category';
            updated.operation = '=';
            updated.value = '';
          }
          return updated;
        }

        if (c.withConditions && c.withConditions.length > 0) {
          return { ...c, withConditions: updateRecursively(c.withConditions) };
        }
        return c;
      });
    };

    setFormData((prev) => ({
      ...(prev as UpdatedVirtualFormData),
      withConditions: updateRecursively(prev.withConditions || []),
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!virtual || !formData) return;

    // Basic validation for required fields
    if (formData.modeType === 'having') {
      if (!formData.operation || !formData.value || !formData.field) {
        alert('Please complete Having configuration before saving.');
        return;
      }
    }

    // Build UpdateVirtualGroupRequest
    try {
      setSubmitting(true);

      let updateReq: UpdateVirtualGroupRequest;

      // If modeType is 'having' or 'all' -> update with simple fields
      if (formData.modeType === 'having' || formData.modeType === 'all') {
        updateReq = {
          id: virtual.id,
          model_name: formData.model,
          field: formData.field || '',
          operator: formData.operation || '',
          value: formData.value || '',
        };
      } else if (formData.modeType === 'with') {
        // In 'with' mode, build nested structure
        if (!formData.withConditions || formData.withConditions.length === 0) {
          alert("Please add at least one 'With' condition.");
          setSubmitting(false);
          return;
        }

        // Transform the first condition into nested request
        const topCond = formData.withConditions[0];
        const nestedReq = transformToNestedRequest(topCond);
        
        updateReq = {
          id: virtual.id,
          ...nestedReq,
        };
      } else {
        setSubmitting(false);
        return;
      }

      const updated = await virtualService.updateVirtual(virtual.id, updateReq);
      onSave(updated);
      onClose();
      alert('Virtual group updated!');
    } catch (err) {
      console.error('Failed to update virtual group:', err);
      alert('Error updating virtual group. See console for details.');
    } finally {
      setSubmitting(false);
    }
  };

  if (!virtual) return null;

  return (
    <Modal
      isOpen={isOpen}
      onClose={() => {
        if (!submitting) onClose();
      }}
      title={`Edit Virtual Group (ID: ${virtual.id})`}
      subtitle="Update virtual group configuration and nested conditions"
      size="xl"
      footer={
        <div className="flex items-center justify-end space-x-3">
          <button
            type="button"
            onClick={() => onClose()}
            disabled={submitting}
            className="inline-flex items-center px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50"
          >
            Cancel
          </button>
          <button
            type="submit"
            form="edit-virtual-form"
            disabled={submitting}
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
          >
            {submitting ? (
              <>
                <i className="ri-loader-4-line animate-spin mr-2" />
                Updating...
              </>
            ) : (
              <>
                <i className="ri-save-line mr-2" />
                Update Virtual Group
              </>
            )}
          </button>
        </div>
      }
    >
      <form id="edit-virtual-form" onSubmit={handleSubmit} className="space-y-6">
        {/* Basic Info Grid - Same as VirtualSearchForm */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Name (UI Only)</label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg"
              placeholder="Descriptive Name"
              disabled={submitting}
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Base Model *</label>
            <select
              value={formData.model}
              onChange={(e) => handleInputChange('model', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg"
              required
              disabled={submitting}
            >
              {MODEL_TYPE_OPTIONS.map(o => <option key={o.value} value={o.value}>{o.label}</option>)}
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Mode *</label>
            <select
              value={formData.modeType}
              onChange={(e) => handleInputChange('modeType', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg"
              required
              disabled={submitting}
            >
              {MODE_TYPE_OPTIONS.map(o => <option key={o.value} value={o.value}>{o.label}</option>)}
            </select>
          </div>
        </div>

        {/* HAVING MODE - Same as VirtualSearchForm */}
        {formData.modeType === 'having' && (
          <div className="p-4 bg-gray-50 rounded-lg border border-gray-200">
            <HavingFields data={formData} onChange={(field, value) => handleInputChange(field, value)} />
          </div>
        )}

        {/* WITH MODE - Same as VirtualSearchForm */}
        {formData.modeType === 'with' && (
          <div className="bg-gray-50 rounded-lg p-6 border border-gray-200 space-y-4">
            <h3 className="text-lg font-semibold text-gray-900 flex items-center">
              <i className="ri-settings-line mr-2 text-gray-600"></i>
              With Configuration
            </h3>
            {(formData.withConditions || []).map(condition => (
              <WithConditionGroup key={condition.id} condition={condition} onChange={handleWithConditionChange} />
            ))}
          </div>
        )}

        
        {/* Info about original ID */}
        <div className="text-sm text-gray-500 italic">
          Editing Virtual Group ID: {virtual.id}
        </div>
      </form>
    </Modal>
  );
}





