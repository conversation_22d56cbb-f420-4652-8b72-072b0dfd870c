# Virtual Groups API Integration

## Overview
This document describes the API integration for the Virtual Groups feature in the Kindali Dashboard.

## API Endpoint
**Base URL:** `http://**************:30004/v1/virtual-groups`

## API Structure

### Request Format (Create/Update)
```json
{
  "model_name": "Hotel",
  "has_child": false,
  "has_parent": true,
  "is_function": true,
  "function": "COUNT",
  "field": "city",
  "operator": "=",
  "value": "kochi",
  "parent_id": 1
}
```

### Response Format
```json
{
  "id": 5,
  "created_at": "2025-10-17T09:41:31.192182739Z",
  "updated_at": "2025-10-17T09:41:31.192182739Z",
  "deleted_at": null,
  "model_name": "Hotel",
  "has_child": false,
  "has_parent": true,
  "is_function": true,
  "function": "COUNT",
  "field": "city",
  "operator": "=",
  "value": "kochi",
  "parent_id": 1
}
```

## Field Mapping

### API → Frontend
| API Field | Frontend Field | Description |
|-----------|---------------|-------------|
| `id` | `id` | Unique identifier |
| `model_name` | `model` | Model type (Hotel, Flight, All) |
| `has_parent` | `modeType` | If true → 'with', else check is_function |
| `is_function` | `functionType` | If true → 'Function', else 'Field' |
| `function` | `function` | Function type (COUNT, AVG, etc.) |
| `field` | `field` | Field name |
| `operator` | `operation` | Comparison operator |
| `value` | `value` | Comparison value |
| `created_at` | `created_at` | Creation timestamp |
| `updated_at` | `updated_at` | Update timestamp |

### Frontend → API
| Frontend Field | API Field | Transformation |
|---------------|-----------|----------------|
| `model` | `model_name` | Direct mapping |
| `modeType` | `has_parent` | 'with' → true, else false |
| `functionType` | `is_function` | 'Function' → true, 'Field' → false |
| `function` | `function` | Direct mapping (optional) |
| `field` | `field` | Direct mapping |
| `operation` | `operator` | Direct mapping |
| `value` | `value` | Direct mapping |

## API Methods

### 1. Get All Virtual Groups
```typescript
GET /v1/virtual-groups
Response: VirtualApiResponse[]
```

### 2. Get Virtual Group by ID
```typescript
GET /v1/virtual-groups/{id}
Response: VirtualApiResponse
```

### 3. Create Virtual Group
```typescript
POST /v1/virtual-groups
Body: CreateVirtualApiRequest
Response: VirtualApiResponse
```

### 4. Update Virtual Group
```typescript
PUT /v1/virtual-groups/{id}
Body: CreateVirtualApiRequest
Response: VirtualApiResponse
```

### 5. Delete Virtual Group
```typescript
DELETE /v1/virtual-groups/{id}
Response: void
```

## Data Transformation

### VirtualUtils.fromApiResponse()
Converts API response to frontend Virtual interface:
- Generates a name from ID (API doesn't provide name)
- Maps `model_name` to `model`
- Determines `modeType` based on `has_parent` and `is_function`
- Maps `operator` to `operation`
- Sets default status as 'ACTIVE'

### VirtualUtils.toApiRequest()
Converts frontend form data to API request:
- Maps `model` to `model_name`
- Sets `has_child` to false (default)
- Sets `has_parent` based on `modeType === 'with'`
- Sets `is_function` based on `functionType === 'Function'`
- Maps `operation` to `operator`

## Files Modified

### 1. `virtual.model.ts`
- Added `VirtualApiResponse` interface
- Added `CreateVirtualApiRequest` interface
- Added `VirtualUtils.fromApiResponse()` method
- Added `VirtualUtils.toApiRequest()` method

### 2. `virtual.service.ts`
- Updated `getVirtuals()` to use API
- Updated `getVirtualById()` to use API
- Updated `createVirtual()` to use API with data transformation
- Updated `updateVirtual()` to use API with data transformation
- Updated `deleteVirtual()` to use API
- Removed mock data dependency

### 3. `axiosInstancevirtual.ts`
- Already configured with base URL: `http://**************:30004`
- Includes authentication token handling
- Includes error interceptors

### 4. `api-service.ts`
- Already includes virtual API methods:
  - `getvirtual<T>()`
  - `postvirtual<T>()`
  - `putvirtual<T>()`
  - `deletevirtual<T>()`

## Usage Example

### Creating a Virtual Group
```typescript
const formData: VirtualFormData = {
  name: 'Hotel City Filter',
  model: 'Hotel',
  modeType: 'having',
  functionType: 'Function',
  function: 'count',
  field: 'city',
  operation: '==',
  value: 'kochi',
  status: 'ACTIVE'
};

const virtual = await virtualService.createVirtual(formData);
```

### Fetching Virtual Groups
```typescript
const response = await virtualService.getVirtuals(1, 10);
console.log(response.data); // Array of Virtual objects
```

## Notes

1. **Name Field**: The API doesn't have a `name` field, so we generate it as `Virtual {id}` in the frontend.

2. **Status Field**: The API doesn't have a status field, so we default to 'ACTIVE' in the frontend.

3. **Parent/Child Relationships**: The API supports hierarchical structures via `parent_id`, `has_parent`, and `has_child` fields.

4. **Function vs Field**: The `is_function` flag determines whether the virtual group uses a function (COUNT, AVG, etc.) or just a field comparison.

5. **Operator Mapping**: Frontend uses `operation` while API uses `operator` - both are mapped correctly.

## Testing

To test the API integration:

1. Create a new virtual search using the form
2. Check browser network tab for API calls to `/v1/virtual-groups`
3. Verify request body matches the expected format
4. Verify response is correctly transformed to frontend format
5. Check that the list updates after create/update/delete operations

## Error Handling

All API methods include try-catch blocks with console error logging. Errors are propagated to the UI for user feedback.
