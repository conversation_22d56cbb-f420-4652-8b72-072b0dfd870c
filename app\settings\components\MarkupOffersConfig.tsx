// 'use client';

// import React from 'react';

// interface MarkupSettings {
//   enableMarkupsOnList: boolean;
//   markupCalculationBase: 'base_price' | 'total_price';
// }

// interface Props {
//   settings: MarkupSettings;
//   onToggleListDisplay: () => void;
//   onBaseChange: (base: 'base_price' | 'total_price') => void;
// }

// const MarkupOffersConfig: React.FC<Props> = ({ settings, onToggleListDisplay, onBaseChange }) => {
//   return (
//     <div className="animate-fade-in bg-white rounded-lg shadow-sm border border-gray-200">
//       <div className="p-6 border-b border-gray-200 flex items-center space-x-3">
//         <div className="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center">
//           <i className="ri-price-tag-3-line text-yellow-600 text-lg"></i>
//         </div>
//         <div>
//           <h2 className="text-lg font-semibold text-gray-900">Markup & Offers Configuration</h2>
//           <p className="text-sm text-gray-600">Configure markup display and calculation preferences</p>
//         </div>
//       </div>

//       <div className="p-6 space-y-6">
//         {/* Enable/Disable Markups on List Page */}
//         <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg border border-gray-200">
//           <div className="flex-1">
//             <h3 className="text-sm font-semibold text-gray-900 mb-1">Display Markups on List Page</h3>
//             <p className="text-sm text-gray-600">
//               Show or hide markup information on the hotel/service listing pages
//             </p>
//           </div>
//           <div className="flex items-center ml-4">
//             <button
//               onClick={onToggleListDisplay}
//               className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
//                 settings.enableMarkupsOnList ? 'bg-purple-600' : 'bg-gray-300'
//               }`}
//             >
//               <span
//                 className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
//                   settings.enableMarkupsOnList ? 'translate-x-6' : 'translate-x-1'
//                 }`}
//               />
//             </button>
//             <span className="ml-3 text-sm font-medium text-gray-700">
//               {settings.enableMarkupsOnList ? 'Enabled' : 'Disabled'}
//             </span>
//           </div>
//         </div>

//         {/* Markup Calculation Base */}
//         <div className="p-4 bg-gray-50 rounded-lg border border-gray-200">
//           <h3 className="text-sm font-semibold text-gray-900 mb-3">Markup Calculation Base</h3>
//           <p className="text-sm text-gray-600 mb-4">
//             Choose whether markup should be calculated on the base price or total price (including taxes and fees)
//           </p>

//           <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
//             {['base_price', 'total_price'].map((type) => (
//               <button
//                 key={type}
//                 onClick={() => onBaseChange(type as 'base_price' | 'total_price')}
//                 className={`p-4 rounded-lg border-2 transition-all ${
//                   settings.markupCalculationBase === type
//                     ? 'border-purple-600 bg-purple-50'
//                     : 'border-gray-200 bg-white hover:border-gray-300'
//                 }`}
//               >
//                 <div className="flex items-center space-x-3">
//                   <div
//                     className={`w-5 h-5 rounded-full border-2 flex items-center justify-center ${
//                       settings.markupCalculationBase === type
//                         ? 'border-purple-600'
//                         : 'border-gray-300'
//                     }`}
//                   >
//                     {settings.markupCalculationBase === type && (
//                       <div className="w-3 h-3 rounded-full bg-purple-600"></div>
//                     )}
//                   </div>
//                   <div className="text-left flex-1">
//                     <div className="font-semibold text-gray-900 capitalize">
//                       {type.replace('_', ' ')}
//                     </div>
//                   </div>
//                 </div>
//               </button>
//             ))}
//           </div>
//         </div>
//       </div>
//     </div>
//   );
// };

// export default MarkupOffersConfig;

'use client';

import React from 'react';

interface MarkupSettings {
  enableMarkupsOnList: boolean;
  markupCalculationBase: 'base' | 'total';
}

interface Props {
  settings: MarkupSettings;
  onToggleListDisplay: () => void;
  onBaseChange: (base: 'base' | 'total') => void;
}

const MarkupOffersConfig: React.FC<Props> = ({ settings, onToggleListDisplay, onBaseChange }) => {
  return (
    <div className="animate-fade-in bg-white rounded-lg shadow border border-gray-200">
      {/* Header */}
      <div className="p-6 border-b border-gray-200 flex items-center space-x-3">
        <div className="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center">
          <i className="ri-price-tag-3-line text-yellow-600 text-lg"></i>
        </div>
        <div>
          <h2 className="text-lg font-semibold text-gray-900">Markup & Offers Configuration</h2>
          <p className="text-sm text-gray-600">Configure markup display and calculation preferences</p>
        </div>
      </div>

      <div className="p-6 space-y-6">
        {/* Enable/Disable Markups on List Page */}
        <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg border border-gray-200">
          <div className="flex-1">
            <h3 className="text-sm font-semibold text-gray-900 mb-1">Display Markups on List Page</h3>
            <p className="text-sm text-gray-600">
              Show or hide markup information on the hotel/service listing pages
            </p>
          </div>
          <div className="flex items-center ml-4">
            <button
              onClick={onToggleListDisplay}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                settings.enableMarkupsOnList ? 'bg-purple-600' : 'bg-gray-300'
              }`}
            >
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  settings.enableMarkupsOnList ? 'translate-x-6' : 'translate-x-1'
                }`}
              />
            </button>
            <span className="ml-3 text-sm font-medium text-gray-700">
              {settings.enableMarkupsOnList ? 'Enabled' : 'Disabled'}
            </span>
          </div>
        </div>

        {/* Markup Calculation Base */}
        <div className="p-4 bg-gray-50 rounded-lg border border-gray-200">
          <h3 className="text-sm font-semibold text-gray-900 mb-3">Markup Calculation Base</h3>
          <p className="text-sm text-gray-600 mb-4">
            Choose whether markup should be calculated on the base price or total price (including taxes and fees)
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {(['base', 'total'] as const).map((type) => (
              <button
                key={type}
                onClick={() => onBaseChange(type)}
                className={`p-4 rounded-lg border-2 transition-all ${
                  settings.markupCalculationBase === type
                    ? 'border-purple-600 bg-purple-50'
                    : 'border-gray-200 bg-white hover:border-gray-300'
                }`}
              >
                <div className="flex items-center space-x-3">
                  <div
                    className={`w-5 h-5 rounded-full border-2 flex items-center justify-center ${
                      settings.markupCalculationBase === type ? 'border-purple-600' : 'border-gray-300'
                    }`}
                  >
                    {settings.markupCalculationBase === type && (
                      <div className="w-3 h-3 rounded-full bg-purple-600"></div>
                    )}
                  </div>
                  <div className="text-left flex-1">
                    <div className="font-semibold text-gray-900 capitalize">
                      {type === 'base' ? 'Base Price' : 'Total Price'}
                    </div>
                  </div>
                </div>
              </button>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default MarkupOffersConfig;
