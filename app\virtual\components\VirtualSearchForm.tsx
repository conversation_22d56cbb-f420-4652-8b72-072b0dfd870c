


  // 'use client';

  // import React, { useState, forwardRef, useImperativeHandle, useEffect } from 'react';
  // import {
  //   Virtual,
  //   CreateVirtualRequest,
  //   MODEL_TYPE_OPTIONS,
  //   MODE_TYPE_OPTIONS,
  //   FUNCTION_TYPE_OPTIONS,
  //   FUNCTION_OPTIONS,
  //   FIELD_OPTIONS,
  //   OPERATION_OPTIONS
  // } from '../virtual.model';
  // import virtualService from '../virtual.service';

  // // --- Type Definitions (Now Recursive) ---
  // export interface WithCondition {
  //   id: number;
  //   model: string;
  //   modeType: 'all' | 'having' | 'with';
  //   functionType?: 'Function' | 'Field';
  //   function?: string;
  //   field?: string;
  //   operation?: string;
  //   value?: string;
  //   withConditions?: WithCondition[]; // Allows nesting
  // }

  // export interface UpdatedVirtualFormData {
  //   name: string;
  //   model: string;
  //   modeType: 'all' | 'having' | 'with';
  //   status: 'ACTIVE' | 'INACTIVE';
  //   withConditions: WithCondition[];
  //   functionType?: 'Function' | 'Field';
  //   function?: string;
  //   field?: string;
  //   operation?: string;
  //   value?: string;
  // }

  // export interface VirtualSearchFormRef {
  //   resetForm: () => void;
  // }

  // interface VirtualSearchFormProps {
  //   onVirtualCreated?: (virtual: Virtual) => void;
  // }

  // // --- Reusable UI Component for 'Having' Logic ---
  // const HavingFields = ({ data, onChange, isNested = false }: { data: any, onChange: (field: any, value: any) => void, isNested?: boolean }) => (
  //     <div className={`rounded-lg p-6 border ${isNested ? 'bg-white mt-4' : 'bg-blue-50 border-blue-200'}`}>
  //         <h3 className="text-md font-semibold text-gray-800 mb-4 flex items-center">
  //             <i className="ri-function-line mr-2 text-blue-600"></i>
  //             Having Configuration
  //         </h3>
  //         <div className="space-y-4">
  //             <div>
  //                 <label className="block text-sm font-medium text-gray-700 mb-2">Function Type *</label>
  //                 <select value={data.functionType || ''} onChange={(e) => onChange('functionType', e.target.value)} className="w-full px-3 py-2 border border-gray-300 rounded-lg" required>
  //                     <option value="">Select Type</option>
  //                     {FUNCTION_TYPE_OPTIONS.map(o => <option key={o.value} value={o.value}>{o.label}</option>)}
  //                 </select>
  //             </div>
  //             {data.functionType === 'Function' && (
  //                 <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
  //                     {/* Function, Field, Operation, Value inputs */}
  //                     <div>
  //                         <label className="block text-sm font-medium text-gray-700 mb-2">Function *</label>
  //                         <select value={data.function || ''} onChange={(e) => onChange('function', e.target.value)} className="w-full px-3 py-2 border border-gray-300 rounded-lg" required>
  //                             <option value="">Select Function</option>
  //                             {FUNCTION_OPTIONS.map(o => <option key={o.value} value={o.value}>{o.label}</option>)}
  //                         </select>
  //                     </div>
  //                     <div>
  //                         <label className="block text-sm font-medium text-gray-700 mb-2">Field *</label>
  //                         <select value={data.field || ''} onChange={(e) => onChange('field', e.target.value)} className="w-full px-3 py-2 border border-gray-300 rounded-lg" required>
  //                             <option value="">Select Field</option>
  //                             {FIELD_OPTIONS.map(o => <option key={o.value} value={o.value}>{o.label}</option>)}
  //                         </select>
  //                     </div>
  //                     <div>
  //                         <label className="block text-sm font-medium text-gray-700 mb-2">Operation *</label>
  //                         <select value={data.operation || ''} onChange={(e) => onChange('operation', e.target.value)} className="w-full px-3 py-2 border border-gray-300 rounded-lg" required>
  //                             <option value="">Select Operation</option>
  //                             {OPERATION_OPTIONS.map(o => <option key={o.value} value={o.value}>{o.label}</option>)}
  //                         </select>
  //                     </div>
  //                     <div>
  //                         <label className="block text-sm font-medium text-gray-700 mb-2">Value *</label>
  //                         <input type="text" value={data.value || ''} onChange={(e) => onChange('value', e.target.value)} className="w-full px-3 py-2 border border-gray-300 rounded-lg" placeholder="Enter value" required />
  //                     </div>
  //                 </div>
  //             )}
  //             {data.functionType === 'Field' && (
  //                 <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
  //                      {/* Field, Operation, Value inputs */}
  //                     <div>
  //                         <label className="block text-sm font-medium text-gray-700 mb-2">Field *</label>
  //                         <select value={data.field || ''} onChange={(e) => onChange('field', e.target.value)} className="w-full px-3 py-2 border border-gray-300 rounded-lg" required>
  //                            <option value="">Select Field</option>
  //                            {FIELD_OPTIONS.map(o => <option key={o.value} value={o.value}>{o.label}</option>)}
  //                         </select>
  //                     </div>
  //                     <div>
  //                         <label className="block text-sm font-medium text-gray-700 mb-2">Operation *</label>
  //                         <select value={data.operation || ''} onChange={(e) => onChange('operation', e.target.value)} className="w-full px-3 py-2 border border-gray-300 rounded-lg" required>
  //                            <option value="">Select Operation</option>
  //                            {OPERATION_OPTIONS.map(o => <option key={o.value} value={o.value}>{o.label}</option>)}
  //                         </select>
  //                     </div>
  //                     <div>
  //                         <label className="block text-sm font-medium text-gray-700 mb-2">Value *</label>
  //                         <input type="text" value={data.value || ''} onChange={(e) => onChange('value', e.target.value)} className="w-full px-3 py-2 border border-gray-300 rounded-lg" placeholder="Enter value" required />
  //                     </div>
  //                 </div>
  //             )}
  //         </div>
  //     </div>
  // );


  // // --- Recursive Component for 'With' Logic ---
  // const WithConditionGroup = ({ condition, onChange }: { condition: WithCondition, onChange: (id: number, field: keyof WithCondition, value: any) => void }) => {
  //   return (
  //     <div className="bg-white p-4 rounded-md border border-gray-200">
  //       <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
  //         <div>
  //           <label className="block text-sm font-medium text-gray-700 mb-2">Model *</label>
  //           <select value={condition.model} onChange={(e) => onChange(condition.id, 'model', e.target.value)} className="w-full px-3 py-2 border border-gray-300 rounded-lg" required>
  //             {MODEL_TYPE_OPTIONS.map(o => <option key={o.value} value={o.value}>{o.label}</option>)}
  //           </select>
  //         </div>
  //         <div>
  //           <label className="block text-sm font-medium text-gray-700 mb-2">Mode *</label>
  //           <select value={condition.modeType} onChange={(e) => onChange(condition.id, 'modeType', e.target.value)} className="w-full px-3 py-2 border border-gray-300 rounded-lg" required>
  //             {/* CORRECTED: The filter is removed to allow nesting 'with' */}
  //             {MODE_TYPE_OPTIONS.map(o => <option key={o.value} value={o.value}>{o.label}</option>)}
  //           </select>
  //         </div>
  //       </div>
  //       {condition.modeType === 'having' && <HavingFields data={condition} onChange={(field, value) => onChange(condition.id, field, value)} isNested={true} />}
  //       {condition.modeType === 'with' && (
  //         <div className="mt-4 pl-4 border-l-2 border-gray-200">
  //              <h4 className="text-sm font-semibold text-gray-600 mb-2">Nested Condition</h4>
  //             {condition.withConditions?.map(nestedCondition => (
  //                 <WithConditionGroup key={nestedCondition.id} condition={nestedCondition} onChange={onChange} />
  //             ))}
  //         </div>
  //       )}
  //     </div>
  //   );
  // };


  // // --- Main Form Component ---
  // const VirtualSearchForm = forwardRef<VirtualSearchFormRef, VirtualSearchFormProps>(({ onVirtualCreated }, ref) => {
  //   const initialFormData: UpdatedVirtualFormData = { name: '', model: 'All', modeType: 'all', status: 'ACTIVE', withConditions: [] };
  //   const [formData, setFormData] = useState<UpdatedVirtualFormData>(initialFormData);
  //   const [virtuals, setVirtuals] = useState<Virtual[]>([]);
  //   const [loading, setLoading] = useState(false);
  //   const [submitting, setSubmitting] = useState(false);

  //   useEffect(() => { loadVirtuals(); }, []);
  //   const loadVirtuals = async () => { /* ... unchanged ... */ };
    
  //   const handleInputChange = (field: keyof UpdatedVirtualFormData, value: any) => {
  //     setFormData(prev => {
  //       const newData = { ...prev, [field]: value };
  //       if (field === 'modeType') {
  //         if (value !== 'having') { delete newData.functionType; delete newData.function; delete newData.field; delete newData.operation; delete newData.value; }
  //         if (value === 'with') { newData.withConditions = [{ id: Date.now(), model: 'All', modeType: 'all' }]; } 
  //         else { newData.withConditions = []; }
  //       }
  //       if (field === 'functionType') { delete newData.function; delete newData.field; delete newData.operation; delete newData.value; }
  //       return newData;
  //     });
  //   };

  //   const handleWithConditionChange = (id: number, field: keyof WithCondition, value: any) => {
  //     const updateRecursively = (conditions: WithCondition[]): WithCondition[] => {
  //         return conditions.map(condition => {
  //             if (condition.id === id) {
  //                 const updatedCondition: WithCondition = { ...condition, [field]: value };
  //                 if (field === 'modeType') {
  //                     // Clear other mode data
  //                     delete updatedCondition.functionType; delete updatedCondition.function; delete updatedCondition.field; delete updatedCondition.operation; delete updatedCondition.value;
  //                     // Handle switching to/from 'with'
  //                     if (value === 'with') {
  //                         updatedCondition.withConditions = [{ id: Date.now(), model: 'All', modeType: 'all' }];
  //                     } else {
  //                         delete updatedCondition.withConditions;
  //                     }
  //                 }
  //                  if (field === 'functionType') { delete updatedCondition.function; delete updatedCondition.field; delete updatedCondition.operation; delete updatedCondition.value;}
  //                 return updatedCondition;
  //             }
  //             // If the condition has nested conditions, recurse
  //             if (condition.withConditions) {
  //                 return { ...condition, withConditions: updateRecursively(condition.withConditions) };
  //             }
  //             return condition;
  //         });
  //     };
  //     setFormData(prev => ({ ...prev, withConditions: updateRecursively(prev.withConditions) }));
  //   };
    
  //   // Helper to clean data for submission
  //   const cleanConditionsForSubmission = (conditions: WithCondition[]): any[] => {
  //     return conditions.map(({ id, ...rest }) => {
  //         if (rest.withConditions) {
  //             rest.withConditions = cleanConditionsForSubmission(rest.withConditions);
  //         }
  //         return rest;
  //     });
  //   };

  //   const handleSubmit = async (e: React.FormEvent) => {
  //     e.preventDefault();
  //     if (!formData.name.trim()) return alert('Please enter a name');
  //     setSubmitting(true);
  //     try {
  //       const createData: any = { name: formData.name, model: formData.model, modeType: formData.modeType, status: formData.status };
  //       if (formData.modeType === 'having') { /* ... unchanged ... */ } 
  //       else if (formData.modeType === 'with') {
  //         createData.withConditions = cleanConditionsForSubmission(formData.withConditions);
  //       }
  //       // ... API call and form reset logic ...
  //     } finally { setSubmitting(false); }
  //   };
    
  //   const resetForm = () => setFormData(initialFormData);
  //   useImperativeHandle(ref, () => ({ resetForm }));
  //   const handleDelete = async (id: number) => { /* ... unchanged ... */ };

  //   return (
  //     <div className="space-y-6">
  //       <div className="bg-white rounded-lg shadow-sm border border-gray-200">
  //         <div className="p-6">
  //           <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center"><i className="ri-add-line mr-2 text-blue-600"></i>Create Virtual Search</h2>
  //           <form onSubmit={handleSubmit} className="space-y-6">
  //             {/* --- BASIC FIELDS --- */}
  //             <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
  //               <div>
  //                 <label className="block text-sm font-medium text-gray-700 mb-2">Name *</label>
  //                 <input type="text" value={formData.name} onChange={(e) => handleInputChange('name', e.target.value)} className="w-full px-3 py-2 border border-gray-300 rounded-lg" required />
  //               </div>
  //               <div>
  //                 <label className="block text-sm font-medium text-gray-700 mb-2">Base Model *</label>
  //                 <select value={formData.model} onChange={(e) => handleInputChange('model', e.target.value)} className="w-full px-3 py-2 border border-gray-300 rounded-lg" required>
  //                   {MODEL_TYPE_OPTIONS.map(o => <option key={o.value} value={o.value}>{o.label}</option>)}
  //                 </select>
  //               </div>
  //               <div>
  //                 <label className="block text-sm font-medium text-gray-700 mb-2">Mode *</label>
  //                 <select value={formData.modeType} onChange={(e) => handleInputChange('modeType', e.target.value)} className="w-full px-3 py-2 border border-gray-300 rounded-lg" required>
  //                   {MODE_TYPE_OPTIONS.map(o => <option key={o.value} value={o.value}>{o.label}</option>)}
  //                 </select>
  //               </div>
  //             </div>
  //             {/* --- HAVING MODE --- */}
  //             {formData.modeType === 'having' && <HavingFields data={formData} onChange={(field, value) => handleInputChange(field, value)} />}
  //             {/* --- WITH MODE (RECURSIVE) --- */}
  //             {formData.modeType === 'with' && (
  //               <div className="bg-gray-50 rounded-lg p-6 border border-gray-200">
  //                 <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center"><i className="ri-settings-line mr-2 text-gray-600"></i>With Configuration</h3>
  //                 {formData.withConditions.map(condition => (
  //                   <WithConditionGroup key={condition.id} condition={condition} onChange={handleWithConditionChange} />
  //                 ))}
  //               </div>
  //             )}
  //             {/* --- SUBMIT BUTTON --- */}
  //             <div className="flex items-center justify-end pt-6 border-t border-gray-200">
  //               <button type="submit" disabled={submitting} className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors shadow-sm disabled:opacity-50">
  //                 {submitting ? <><i className="ri-loader-4-line animate-spin mr-2"></i>Creating...</> : <><i className="ri-add-line mr-2"></i>Create</>}
  //               </button>
  //             </div>
  //           </form>
  //         </div>
  //       </div>
  //       {/* ... VIRTUAL LIST ... */}
  //     </div>
  //   );
  // });

  // VirtualSearchForm.displayName = 'VirtualSearchForm';
  // export default VirtualSearchForm;



'use client';

import React, { useState, forwardRef, useImperativeHandle } from 'react';
import {
  WithCondition,
  UpdatedVirtualFormData,
  VirtualSearchFormRef,
  VirtualGroup,

  CreateVirtualGroupRequest,
  MODEL_TYPE_OPTIONS,
  FUNCTION_OPTIONS,
  FIELD_OPTIONS,
  OPERATION_OPTIONS,
} from '../virtual.model'; // Adjust path
import virtualService from '../virtual.service'; // Adjust path

// --- UI-Specific Constants ---
const MODE_TYPE_OPTIONS = [
  { value: 'all', label: 'All' },
  { value: 'having', label: 'Having' },
  { value: 'with', label: 'With' },
] as const;

const FUNCTION_TYPE_OPTIONS = [
  { value: 'Function', label: 'Function' },
  { value: 'Field', label: 'Field' },
] as const;

// --- Define the correct nested API request type ---
// Simplified structure: only model_name, field, operator, value, has_child, child
type NestedCreateVirtualGroupRequest = {
  model_name: string;
  field: string;
  operator: string;
  value: string;
  has_child?: boolean;
  child?: NestedCreateVirtualGroupRequest;
};

// --- Child Components ---
// (HavingFields and WithConditionGroup components remain unchanged)
// ... (Paste HavingFields component here) ...
// ... (Paste WithConditionGroup component here) ...

const HavingFields = ({ data, onChange, isNested = false }: { data: any, onChange: (field: any, value: any) => void, isNested?: boolean }) => (
  <div className={`rounded-lg p-6 border ${isNested ? 'bg-white mt-4' : 'bg-blue-50 border-blue-200'}`}>
    <h3 className="text-md font-semibold text-gray-800 mb-4 flex items-center">
      <i className="ri-function-line mr-2 text-blue-600"></i>
      Having Configuration
    </h3>
    <div className="space-y-4">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">Function Type *</label>
        <select value={data.functionType || ''} onChange={(e) => onChange('functionType', e.target.value)} className="w-full px-3 py-2 border border-gray-300 rounded-lg" required>
          <option value="">Select Type</option>
          {FUNCTION_TYPE_OPTIONS.map(o => <option key={o.value} value={o.value}>{o.label}</option>)}
        </select>
      </div>
      {data.functionType === 'Function' && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Function *</label>
            <select value={data.function || ''} onChange={(e) => onChange('function', e.target.value)} className="w-full px-3 py-2 border border-gray-300 rounded-lg" required>
              <option value="">Select Function</option>
              {FUNCTION_OPTIONS.map(o => <option key={o.value} value={o.value}>{o.label}</option>)}
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Field</label>
            <select value={data.field || ''} onChange={(e) => onChange('field', e.target.value)} className="w-full px-3 py-2 border border-gray-300 rounded-lg">
              <option value="">Select Field (Optional)</option>
              {FIELD_OPTIONS.map(o => <option key={o.value} value={o.value}>{o.label}</option>)}
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Operation *</label>
            <select value={data.operation || ''} onChange={(e) => onChange('operation', e.target.value)} className="w-full px-3 py-2 border border-gray-300 rounded-lg" required>
              <option value="">Select Operation</option>
              {OPERATION_OPTIONS.map(o => <option key={o.value} value={o.value}>{o.label}</option>)}
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Value *</label>
            <input type="text" value={data.value || ''} onChange={(e) => onChange('value', e.target.value)} className="w-full px-3 py-2 border border-gray-300 rounded-lg" placeholder="Enter value" required />
          </div>
        </div>
      )}
      {data.functionType === 'Field' && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Field *</label>
            <select value={data.field || ''} onChange={(e) => onChange('field', e.target.value)} className="w-full px-3 py-2 border border-gray-300 rounded-lg" required>
              <option value="">Select Field</option>
              {FIELD_OPTIONS.map(o => <option key={o.value} value={o.value}>{o.label}</option>)}
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Operation *</label>
            <select value={data.operation || ''} onChange={(e) => onChange('operation', e.target.value)} className="w-full px-3 py-2 border border-gray-300 rounded-lg" required>
              <option value="">Select Operation</option>
              {OPERATION_OPTIONS.map(o => <option key={o.value} value={o.value}>{o.label}</option>)}
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Value *</label>
            <input type="text" value={data.value || ''} onChange={(e) => onChange('value', e.target.value)} className="w-full px-3 py-2 border border-gray-300 rounded-lg" placeholder="Enter value" required />
          </div>
        </div>
      )}
    </div>
  </div>
);

const WithConditionGroup = ({ condition, onChange }: { condition: WithCondition, onChange: (id: number, field: keyof WithCondition, value: any) => void }) => {
  return (
    <div className="bg-white p-4 rounded-md border border-gray-200 mb-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Model *</label>
          <select value={condition.model} onChange={(e) => onChange(condition.id, 'model', e.target.value)} className="w-full px-3 py-2 border border-gray-300 rounded-lg" required>
            {MODEL_TYPE_OPTIONS.map(o => <option key={o.value} value={o.value}>{o.label}</option>)}
          </select>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Mode *</label>
          <select value={condition.modeType} onChange={(e) => onChange(condition.id, 'modeType', e.target.value)} className="w-full px-3 py-2 border border-gray-300 rounded-lg" required>
            {MODE_TYPE_OPTIONS.map(o => <option key={o.value} value={o.value}>{o.label}</option>)}
          </select>
        </div>
      </div>
      
      {/* Only show Having Configuration when mode is 'having' - not for 'with' mode */}
      {condition.modeType === 'having' && (
        <HavingFields data={condition} onChange={(field, value) => onChange(condition.id, field, value)} isNested={true} />
      )}
      
      {/* If mode is 'with', show nested conditions */}
      {condition.modeType === 'with' && condition.withConditions && condition.withConditions.length > 0 && (
        <div className="mt-4 pl-4 border-l-2 border-gray-200">
          <h4 className="text-sm font-semibold text-gray-600 mb-2">Nested Conditions</h4>
          {condition.withConditions.map(nested => <WithConditionGroup key={nested.id} condition={nested} onChange={onChange} />)}
        </div>
      )}
    </div>
  );
};

interface VirtualSearchFormProps {
  onVirtualCreated: (virtual: VirtualGroup) => void;
}

const VirtualSearchForm = forwardRef<VirtualSearchFormRef, VirtualSearchFormProps>(
  ({ onVirtualCreated }, ref) => {

    const initialFormData: UpdatedVirtualFormData = {
      name: '',
      model: 'Hotel',
      modeType: 'all',
      status: 'ACTIVE',
      withConditions: [],
    };

    const [formData, setFormData] = useState<UpdatedVirtualFormData>(initialFormData);
    const [submitting, setSubmitting] = useState(false);

    useImperativeHandle(ref, () => ({
      resetForm() {
        setFormData(initialFormData);
      }
    }));

    // (handleInputChange, handleWithConditionChange, and handleAddCondition functions remain unchanged)
    // ... (Paste handleInputChange function here) ...
    // ... (Paste handleWithConditionChange function here) ...
    // ... (Paste handleAddCondition function here) ...

    const handleInputChange = (field: keyof UpdatedVirtualFormData, value: any) => {
      setFormData(prev => {
        const newData = { ...prev, [field]: value };
        if (field === 'modeType') {
          if (value !== 'having') {
            delete newData.functionType; delete newData.function; delete newData.field; delete newData.operation; delete newData.value;
          } else {
            newData.functionType = 'Field'; newData.field = 'city'; newData.operation = '='; newData.value = '';
          }
          if (value === 'with') {
            if (!newData.withConditions || newData.withConditions.length === 0) {
              newData.withConditions = [{ id: Date.now(), model: 'Hotel', modeType: 'having', functionType: 'Field', field: 'city', operation: '=', value: '' }];
            }
          } else {
            newData.withConditions = [];
          }
        }
        if (field === 'functionType' && prev.modeType === 'having') {
          if (value === 'Function') {
            delete newData.field; newData.function = 'COUNT';
          } else {
            delete newData.function; newData.field = 'city';
          }
          newData.operation = '='; newData.value = '';
        }
        return newData;
      });
    };
  
    const handleWithConditionChange = (id: number, field: keyof WithCondition, value: any) => {
      const updateRecursively = (conditions: WithCondition[]): WithCondition[] => {
        return conditions.map(condition => {
          if (condition.id === id) {
            const updatedCondition: WithCondition = { ...condition, [field]: value };
            if (field === 'modeType') {
              delete updatedCondition.functionType; delete updatedCondition.function; delete updatedCondition.field; delete updatedCondition.operation; delete updatedCondition.value;
              if (value === 'with') {
                updatedCondition.withConditions = [{ id: Date.now(), model: 'Hotel', modeType: 'having', functionType: 'Field', field: 'city', operation: '=', value: '' }];
              } else {
                delete updatedCondition.withConditions;
              }
            }
            if (field === 'functionType') {
              delete updatedCondition.function; delete updatedCondition.field; delete updatedCondition.operation; delete updatedCondition.value;
              if (value === 'Function') updatedCondition.function = 'COUNT';
              else updatedCondition.field = 'city';
              updatedCondition.operation = '='; updatedCondition.value = '';
            }
            return updatedCondition;
          }
          if (condition.withConditions && condition.withConditions.length > 0) {
            return { ...condition, withConditions: updateRecursively(condition.withConditions) };
          }
          return condition;
        });
      };
      setFormData(prev => ({ ...prev, withConditions: updateRecursively(prev.withConditions || []) }));
    };
  
    const handleAddCondition = () => {
      setFormData(prev => ({
        ...prev,
        withConditions: [
          ...(prev.withConditions || []),
          { id: Date.now(), model: 'Hotel', modeType: 'having', functionType: 'Field', field: 'city', operation: '=', value: '' }
        ]
      }));
    };

    // --- [CORRECTED] ---
    // Recursive transform for the nested 'child' structure
    // Matches the new simplified API structure (no is_function, function, or parent_id)
    const transformToNestedRequest = (cond: WithCondition): NestedCreateVirtualGroupRequest => {
      // Build the basic request object
      const apiData: NestedCreateVirtualGroupRequest = {
        model_name: cond.model,
        field: cond.field || '',      // Empty string for 'with' mode
        operator: cond.operation || '',
        value: cond.value || '',
      };

      // Check if this condition has nested 'with' conditions
      if (cond.modeType === 'with' && cond.withConditions && cond.withConditions.length > 0) {
        // Has children - set has_child and recursively build child
        const childCond = cond.withConditions[0];
        apiData.has_child = true;
        apiData.child = transformToNestedRequest(childCond);
      }
      // If mode is 'having' (leaf node), don't set has_child or child properties

      return apiData;
    };


    // --- [DELETED] ---
    // const transformConditionRecursive = ... (Old flattening logic removed)


    // --- [CORRECTED] ---
    // Updated handleSubmit to use the new nested transformation
    const handleSubmit = async (e: React.FormEvent) => {
      e.preventDefault();
      setSubmitting(true);
      try {
        // This will be an array of our *new* nested request objects
        let requests: NestedCreateVirtualGroupRequest[] = [];

        if (formData.modeType === 'having' || formData.modeType === 'all') {
          // Convert main form data to API request format directly
          const apiData: NestedCreateVirtualGroupRequest = {
            model_name: formData.model,
            field: formData.field || '',
            operator: formData.operation || '',
            value: formData.value || '',
          };
          requests = [apiData];
        } else if (formData.modeType === 'with') {
          if (!formData.withConditions || formData.withConditions.length === 0) {
            alert("Please define at least one condition in 'With' mode.");
            setSubmitting(false);
            return;
          }
          // Transform each top-level condition (from 'Add Condition' button)
          // into its own, separate, nested API request.
          requests = formData.withConditions.map(cond => transformToNestedRequest(cond));
        }

        // Send each generated request sequentially
        for (const req of requests) {
          // The 'virtualService.createVirtual' function must be able to accept
          // the new 'NestedCreateVirtualGroupRequest' type.
          // Using 'as any' to bypass the old 'CreateVirtualGroupRequest' type from import.
          await virtualService.createVirtual(req as any);
        }

        alert('Virtual group(s) created!');
        onVirtualCreated(formData as any); // adjust if needed
        setFormData(initialFormData);
      } catch (error) {
        console.error('Failed to create virtual group:', error);
        alert('Error creating virtual group. Check console for details.');
      } finally {
        setSubmitting(false);
      }
    };

    return (
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        <div className="p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <i className="ri-add-line mr-2 text-blue-600"></i>Create Virtual Group
          </h2>
          <form onSubmit={handleSubmit} className="space-y-6">

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Name (UI Only)</label>
                <input type="text" value={formData.name} onChange={(e) => handleInputChange('name', e.target.value)} className="w-full px-3 py-2 border border-gray-300 rounded-lg" placeholder="Descriptive Name" />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Base Model *</label>
                <select value={formData.model} onChange={(e) => handleInputChange('model', e.target.value)} className="w-full px-3 py-2 border border-gray-300 rounded-lg" required>
                  {MODEL_TYPE_OPTIONS.map(o => <option key={o.value} value={o.value}>{o.label}</option>)}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Mode *</label>
                <select value={formData.modeType} onChange={(e) => handleInputChange('modeType', e.target.value)} className="w-full px-3 py-2 border border-gray-300 rounded-lg" required>
                  {MODE_TYPE_OPTIONS.map(o => <option key={o.value} value={o.value}>{o.label}</option>)}
                </select>
              </div>
            </div>

            {formData.modeType === 'having' && (
              <div className="p-4 bg-gray-50 rounded-lg border border-gray-200">
                <HavingFields data={formData} onChange={(field, value) => handleInputChange(field as any, value)} />
              </div>
            )}

            {formData.modeType === 'with' && (
              <div className="bg-gray-50 rounded-lg p-6 border border-gray-200 space-y-4">
                <h3 className="text-lg font-semibold text-gray-900 flex items-center"><i className="ri-settings-line mr-2 text-gray-600"></i>With Configuration</h3>
                {(formData.withConditions || []).map(condition => (
                  <WithConditionGroup key={condition.id} condition={condition} onChange={handleWithConditionChange} />
                ))}
                <button type="button" onClick={handleAddCondition} className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600">Add Condition</button>
              </div>
            )}

            <div className="flex items-center justify-end pt-6 border-t border-gray-200">
              <button type="submit" disabled={submitting} className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors shadow-sm disabled:opacity-50">
                {submitting ? <><i className="ri-loader-4-line animate-spin mr-2"></i>Creating...</> : <><i className="ri-add-line mr-2"></i>Create Group</>}
              </button>
            </div>
          </form>
        </div>
      </div>
    );
  });

VirtualSearchForm.displayName = 'VirtualSearchForm';
export default VirtualSearchForm;