'use client';

import React, { useState, useEffect } from 'react';
import { 
  Faq, 
  FaqFormData, 
  FaqFormErrors,
  getDefaultFaqFormData,
  validateFaqForm,
  LANGUAGE_OPTIONS,
  SERVICE_OPTIONS,
  PAGE_TYPE_OPTIONS
} from '../faq.model';

interface FaqAddEditProps {
  faq: Faq | null;
  onSave: (formData: FaqFormData) => Promise<void>;
  onCancel: () => void;
  mode: 'add' | 'edit';
}

export default function FaqAddEdit({ 
  faq, 
  onSave, 
  onCancel, 
  mode 
}: FaqAddEditProps) {
  const [formData, setFormData] = useState<FaqFormData>(getDefaultFaqFormData());
  const [errors, setErrors] = useState<FaqFormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Initialize form data
  useEffect(() => {
    if (mode === 'edit' && faq) {
      setFormData({
        service_id: faq.service_id || 1,
        question: faq.question || '',
        answer: faq.answer || '',
        service: faq.service || '',
        page_type: faq.page_type || '',
        language: faq.language || '',
        language_code: faq.language_code || '',
      });
    } else {
      setFormData(getDefaultFaqFormData());
    }
    setErrors({});
  }, [faq, mode]);

  // Handle input changes
  const handleInputChange = (field: keyof FaqFormData, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Clear error for this field
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: undefined
      }));
    }
  };

  // Handle language selection
  const handleLanguageChange = (languageValue: string) => {
    const selectedLanguage = LANGUAGE_OPTIONS.find(lang => lang.value === languageValue);
    if (selectedLanguage) {
      setFormData(prev => ({
        ...prev,
        language: selectedLanguage.value,
        language_code: selectedLanguage.code
      }));
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate form
    const validationErrors = validateFaqForm(formData);
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      return;
    }

    setIsSubmitting(true);
    try {
      await onSave(formData);
    } catch (error: any) {
      setErrors({ submit: error.message || 'An error occurred while saving the FAQ' });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="border-b border-gray-200 pb-4">
        <h3 className="text-lg font-semibold text-gray-900">
          {mode === 'add' ? 'Add New FAQ' : 'Edit FAQ'}
        </h3>
        <p className="text-sm text-gray-600 mt-1">
          {mode === 'add' 
            ? 'Create a new frequently asked question and answer'
            : 'Update the FAQ information'
          }
        </p>
      </div>

      {/* Form */}
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Submit Error */}
        {errors.submit && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4">
            <div className="flex">
              <i className="ri-error-warning-line text-red-400 mr-3 mt-0.5"></i>
              <div>
                <h3 className="text-sm font-medium text-red-800">Error</h3>
                <p className="text-sm text-red-700 mt-1">{errors.submit}</p>
              </div>
            </div>
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Service ID */}
          <div>
            <label htmlFor="service_id" className="block text-sm font-medium text-gray-700 mb-2">
              Service ID <span className="text-red-500">*</span>
            </label>
            <input
              type="number"
              id="service_id"
              min="1"
              value={formData.service_id}
              onChange={(e) => handleInputChange('service_id', parseInt(e.target.value) || 1)}
              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                errors.service_id ? 'border-red-300' : 'border-gray-300'
              }`}
              placeholder="Enter service ID"
            />
            {errors.service_id && (
              <p className="mt-1 text-sm text-red-600">{errors.service_id}</p>
            )}
          </div>

          {/* Service */}
          <div>
            <label htmlFor="service" className="block text-sm font-medium text-gray-700 mb-2">
              Service <span className="text-red-500">*</span>
            </label>
            <select
              id="service"
              value={formData.service}
              onChange={(e) => handleInputChange('service', e.target.value)}
              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                errors.service ? 'border-red-300' : 'border-gray-300'
              }`}
            >
              <option value="">Select a service</option>
              {SERVICE_OPTIONS.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
            {errors.service && (
              <p className="mt-1 text-sm text-red-600">{errors.service}</p>
            )}
          </div>

          {/* Page Type */}
          <div>
            <label htmlFor="page_type" className="block text-sm font-medium text-gray-700 mb-2">
              Page Type <span className="text-red-500">*</span>
            </label>
            <select
              id="page_type"
              value={formData.page_type}
              onChange={(e) => handleInputChange('page_type', e.target.value)}
              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                errors.page_type ? 'border-red-300' : 'border-gray-300'
              }`}
            >
              <option value="">Select a page type</option>
              {PAGE_TYPE_OPTIONS.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
            {errors.page_type && (
              <p className="mt-1 text-sm text-red-600">{errors.page_type}</p>
            )}
          </div>

          {/* Language */}
          <div>
            <label htmlFor="language" className="block text-sm font-medium text-gray-700 mb-2">
              Language <span className="text-red-500">*</span>
            </label>
            <select
              id="language"
              value={formData.language}
              onChange={(e) => handleLanguageChange(e.target.value)}
              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                errors.language ? 'border-red-300' : 'border-gray-300'
              }`}
            >
              <option value="">Select a language</option>
              {LANGUAGE_OPTIONS.map((option) => (
                <option key={option.code} value={option.value}>
                  {option.label} ({option.code.toUpperCase()})
                </option>
              ))}
            </select>
            {errors.language && (
              <p className="mt-1 text-sm text-red-600">{errors.language}</p>
            )}
          </div>
        </div>

        {/* Question */}
        <div>
          <label htmlFor="question" className="block text-sm font-medium text-gray-700 mb-2">
            Question <span className="text-red-500">*</span>
          </label>
          <textarea
            id="question"
            rows={3}
            value={formData.question}
            onChange={(e) => handleInputChange('question', e.target.value)}
            className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
              errors.question ? 'border-red-300' : 'border-gray-300'
            }`}
            placeholder="Enter the frequently asked question"
          />
          {errors.question && (
            <p className="mt-1 text-sm text-red-600">{errors.question}</p>
          )}
        </div>

        {/* Answer */}
        <div>
          <label htmlFor="answer" className="block text-sm font-medium text-gray-700 mb-2">
            Answer <span className="text-red-500">*</span>
          </label>
          <textarea
            id="answer"
            rows={4}
            value={formData.answer}
            onChange={(e) => handleInputChange('answer', e.target.value)}
            className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
              errors.answer ? 'border-red-300' : 'border-gray-300'
            }`}
            placeholder="Enter the answer to the question"
          />
          {errors.answer && (
            <p className="mt-1 text-sm text-red-600">{errors.answer}</p>
          )}
        </div>

        {/* Form Actions */}
        <div className="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            disabled={isSubmitting}
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={isSubmitting}
            className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isSubmitting ? (
              <div className="flex items-center">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                {mode === 'add' ? 'Creating...' : 'Updating...'}
              </div>
            ) : (
              mode === 'add' ? 'Create FAQ' : 'Update FAQ'
            )}
          </button>
        </div>
      </form>
    </div>
  );
}
