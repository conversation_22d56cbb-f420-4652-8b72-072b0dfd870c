// models/virtual.model.ts

export interface VirtualGroup {
  id: number;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
  model_name: string;
  has_child: boolean;
  has_parent: boolean;
  is_function: boolean;
  function?: string; // e.g., "COUNT"
  field?: string;    // e.g., "city"
  operator: string; // e.g., "="
  value: string;
  parent_id?: number | null;

  // Recursive child relation (supports multi-level nesting)
  child?: VirtualGroupChild | null;

  // Optional backward/related connections
  parent?: VirtualGroupParent | null;
  markup_connections?: MarkupConnection[];
  offer_connections?: OfferConnection[];
}

/**
 * Represents nested child objects recursively
 */
export interface VirtualGroupChild {
  id: number;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
  model_name: string;
  has_child: boolean;
  has_parent: boolean;
  is_function: boolean;
  function?: string;
  field?: string;
  operator: string;
  value: string;
  parent_id: number;

  // Recursive nesting (for multi-level)
  child?: VirtualGroupChild | null;
}

/**
 * A simplified parent object as seen in the 'parent' property
 */
export interface VirtualGroupParent {
  id: number;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
  model_name: string;
  has_child: boolean;
  has_parent: boolean;
  is_function: boolean;
  field?: string;
  operator: string;
  value: string;
}

// --- Connection Types ---

export interface MarkupConnection {
  id: number;
  virtual_group_id: number;
  markup_id: number;
}

export interface OfferConnection {
  id: number;
  virtual_group_id: number;
  offer_id: number;
}

// --- API Request Types ---

/**
 * Body for POST /v1/virtual-groups
 * Supports recursive child creation
 * Simplified structure: only model_name, field, operator, value, has_child, and child
 */
export interface CreateVirtualGroupRequest {
  model_name: string;
  field: string;
  operator: string;
  value: string;
  has_child?: boolean;
  child?: CreateVirtualGroupRequest;
}

/**
 * Body for PUT /v1/virtual-groups/{id}
 * Same structure as create, but with optional id field
 */
export interface UpdateVirtualGroupRequest {
  id?: number;
  model_name: string;
  field: string;
  operator: string;
  value: string;
  has_child?: boolean;
  child?: UpdateVirtualGroupRequest;
}

// Form data type
export type VirtualGroupFormData = CreateVirtualGroupRequest;

// --- Dropdown Options (Updated) ---

export const MODEL_TYPE_OPTIONS = [
  { value: 'Hotel', label: 'Hotel' },
  { value: 'Flight', label: 'Flight' },
  { value: 'ParentGroup', label: 'ParentGroup' },
  { value: 'ChildGroup', label: 'ChildGroup' },
  { value: 'GrandChildGroup', label: 'GrandChildGroup' },
  { value: 'GrandGrandChildGroup', label: 'GrandGrandChildGroup' },
] as const;

export const FUNCTION_OPTIONS = [
  { value: 'COUNT', label: 'Count' },
  { value: 'AVG', label: 'Average' },
  { value: 'SUM', label: 'Sum' },
  { value: 'MIN', label: 'Minimum' },
  { value: 'MAX', label: 'Maximum' }
] as const;

export const FIELD_OPTIONS = [  
  { value: 'Category', label: 'Category' },
  { value: 'SubCategory', label: 'Sub Category' },
  { value: 'Item', label: 'Item' },
  { value: 'SubItem', label: 'Sub Item' },
  { value: 'city', label: 'City' },
  { value: 'rating', label: 'Rating' },
  { value: 'price', label: 'Price' },
] as const;

export const OPERATION_OPTIONS = [
  { value: '=', label: 'Equals (=)' },
  { value: '!=', label: 'Not Equals (!=)' },
  { value: '>', label: 'Greater Than (>)' },
  { value: '<', label: 'Less Than (<)' },
  { value: '>=', label: 'Greater or Equal (>=)' },
  { value: '<=', label: 'Less or Equal (<=)' },
] as const;

// --- UI Form Types for Create/Edit Forms ---

/**
 * Recursive condition structure for the UI form (matches VirtualSearchForm)
 */
export interface WithCondition {
  id: number;
  model: string;
  modeType: 'all' | 'having' | 'with';
  functionType?: 'Function' | 'Field';
  function?: string;
  field?: string;
  operation?: string;
  value?: string;
  withConditions?: WithCondition[]; // Allows nesting
}

/**
 * Form data structure used by VirtualSearchForm
 */
export interface UpdatedVirtualFormData {
  name: string;
  model: string;
  modeType: 'all' | 'having' | 'with';
  status: 'ACTIVE' | 'INACTIVE';
  withConditions: WithCondition[];
  functionType?: 'Function' | 'Field';
  function?: string;
  field?: string;
  operation?: string;
  value?: string;
}

/**
 * Ref interface for VirtualSearchForm
 */
export interface VirtualSearchFormRef {
  resetForm: () => void;
}

// --- Utility Class (Updated) ---
export class VirtualUtils {
  static formatDate(dateString: string): string {
    if (!dateString || dateString.startsWith('0001-01-01')) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  static getRuleDescription(group: VirtualGroup | VirtualGroupChild): string {
    if (group.is_function) {
      const field = group.field ? `(${group.field})` : '';
      return `${group.function}${field} ${group.operator} ${group.value}`;
    } else {
      return `${group.field} ${group.operator} ${group.value}`;
    }
  }

  static validate(data: VirtualGroupFormData): string[] {
    const errors: string[] = [];
    if (!data.model_name) errors.push('Model name is required');
    if (!data.field) errors.push('Field is required');
    if (!data.operator) errors.push('Operator is required');
    if (!data.value?.trim()) errors.push('Value is required');

    if (data.is_function && !data.function)
      errors.push('Function is required when is_function is true');

    if (data.has_child && data.child)
      errors.push(...VirtualUtils.validate(data.child));

    return errors;
  }

  /**
   * Transform API response (nested child objects) to UI form structure (withConditions arrays)
   */
  static transformApiToFormData(apiData: VirtualGroup): UpdatedVirtualFormData {
    const formData: UpdatedVirtualFormData = {
      name: apiData.model_name,
      model: apiData.model_name,
      modeType: 'all',
      status: 'ACTIVE',
      withConditions: []
    };

    // Determine mode based on has_child and data presence
    if (apiData.has_child && apiData.child) {
      // If has_child is true → mode is 'with'
      // The parent item becomes the first condition
      formData.modeType = 'with';
      formData.withConditions = [this.transformToWithCondition(apiData)];
    } else if (apiData.function || apiData.field) {
      // If has_child is false and there's data in function or field → mode is 'having'
      formData.modeType = 'having';
      formData.functionType = apiData.is_function ? 'Function' : 'Field';
      formData.function = apiData.function || undefined;
      formData.field = apiData.field || undefined;
      formData.operation = apiData.operator;
      formData.value = apiData.value;
    } else {
      // If has_child is false and no data in function/field → mode is 'all'
      formData.modeType = 'all';
    }

    return formData;
  }

  /**
   * Transform API item (with possible nested children) to UI WithCondition structure
   * IMPORTANT: API requires field/operator/value at EVERY level, so we must store them
   * even when mode='with' (UI won't show them, but we need them for the request)
   */
  private static transformToWithCondition(apiData: VirtualGroup | VirtualGroupChild): WithCondition {
    // Always store field/operator/value data from API (required for request body)
    const condition: WithCondition = {
      id: apiData.id,
      model: apiData.model_name,
      modeType: 'having', // Default to 'having'
      functionType: apiData.is_function ? 'Function' : 'Field',
      function: apiData.function || undefined,
      field: apiData.field || undefined,
      operation: apiData.operator,
      value: apiData.value
    };

    // If this item has a child, set modeType to 'with' and recursively transform
    // Keep field/operation/value for the API request, even though UI won't show them
    if ('has_child' in apiData && apiData.has_child && apiData.child) {
      condition.modeType = 'with';
      condition.withConditions = [this.transformToWithCondition(apiData.child)];
    }

    return condition;
  }
}
