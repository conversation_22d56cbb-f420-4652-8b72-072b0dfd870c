'use client';

import { useState, useEffect } from 'react';
import { 
  Offer, 
  OfferFormData, 
  OFFER_TYPE_OPTIONS, 
  BUSINESS_TYPE_OPTIONS, 
  SERVICE_TYPE_OPTIONS,
  OfferUtils 
} from '../offer.model';
import OfferService from '../offer.service';

interface OfferAddEditProps {
  offer: Offer | null;
  onSave: () => void;
  onCancel: () => void;
  mode?: 'add' | 'edit';
}

export default function OfferAddEdit({ offer, onSave, onCancel, mode = 'add' }: OfferAddEditProps) {
  const [formData, setFormData] = useState<OfferFormData>({
    name: '',
    offer_type: 'PERCENTAGE',
    offer_value: 0,
    business_type: 'B2C',
    service_type: 'HOTEL',
    offer_code: '',
    description: '',
    user_id: null,
    image: null,
    terms_and_conditions: null,
    valid_from: null,
    valid_to: null
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [loading, setLoading] = useState(false);
  const [isGeneratingCode, setIsGeneratingCode] = useState(false);
  const [uploadedImage, setUploadedImage] = useState<File | null>(null);
  const [imagePreviewUrl, setImagePreviewUrl] = useState<string | null>(null);

  useEffect(() => {
    if (offer) {
      setFormData({
        name: offer.name,
        offer_type: offer.offer_type,
        offer_value: offer.offer_value,
        business_type: offer.business_type,
        service_type: offer.service_type,
        offer_code: offer.offer_code,
        description: offer.description,
        user_id: offer.user_id,
        image: offer.image,
        terms_and_conditions: offer.terms_and_conditions,
        valid_from: offer.valid_from ? offer.valid_from.split('T')[0] : null,
        valid_to: offer.valid_to ? offer.valid_to.split('T')[0] : null
      });

      if (offer.image) {
        setImagePreviewUrl(offer.image);
      }
    }
  }, [offer]);

  const handleInputChange = (field: keyof OfferFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  // Handle image upload similar to hotel management
  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setUploadedImage(file);

      // Create preview URL
      const reader = new FileReader();
      reader.onload = (e) => {
        if (e.target?.result) {
          setImagePreviewUrl(e.target.result as string);
        }
      };
      reader.readAsDataURL(file);
    }
  };

  // Handle image removal
  const handleImageRemove = () => {
    setUploadedImage(null);
    setImagePreviewUrl(null);
    setFormData(prev => ({ ...prev, image: null }));
  };



  const generateOfferCode = () => {
    setIsGeneratingCode(true);
    const prefix = formData.name.substring(0, 3).toUpperCase().replace(/[^A-Z]/g, '');
    const newCode = OfferUtils.generateOfferCode(prefix);
    setFormData(prev => ({ ...prev, offer_code: newCode }));
    setIsGeneratingCode(false);
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Offer name is required';
    }

    if (!formData.offer_code.trim()) {
      newErrors.offer_code = 'Offer code is required';
    } else if (!OfferUtils.isValidOfferCode(formData.offer_code)) {
      newErrors.offer_code = 'Offer code must be 4-12 characters, letters and numbers only';
    }

    if (formData.offer_type !== 'FREE_UPGRADE' && formData.offer_value <= 0) {
      newErrors.offer_value = 'Offer value must be greater than 0';
    }

    if (formData.offer_type === 'PERCENTAGE' && formData.offer_value > 100) {
      newErrors.offer_value = 'Percentage discount cannot exceed 100%';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Description is required';
    }

    if (formData.valid_from && formData.valid_to) {
      const fromDate = new Date(formData.valid_from);
      const toDate = new Date(formData.valid_to);
      
      if (fromDate >= toDate) {
        newErrors.valid_to = 'End date must be after start date';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      // Use the image URL from form data (file upload would need a separate endpoint)
      const submitData = {
        ...formData,
        image: formData.image || imagePreviewUrl,
        valid_from: formData.valid_from ? `${formData.valid_from}T00:00:00Z` : null,
        valid_to: formData.valid_to ? `${formData.valid_to}T23:59:59Z` : null
      };

      if (offer) {
        await OfferService.updateOffer(offer.id, submitData);
      } else {
        await OfferService.createOffer(submitData);
      }
      
      onSave();
    } catch (error: any) {
      setErrors({ submit: error.message || 'An error occurred while saving the offer' });
    } finally {
      setLoading(false);
    }
  };

  const inputStyle = "block w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 text-sm";
  const errorInputStyle = "border-red-300 bg-red-50";

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {errors.submit && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex">
            <i className="ri-error-warning-line text-red-400 mr-2"></i>
            <p className="text-sm text-red-700">{errors.submit}</p>
          </div>
        </div>
      )}

      {/* Basic Information */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Offer Name *
          </label>
          <input
            type="text"
            value={formData.name}
            onChange={(e) => handleInputChange('name', e.target.value)}
            placeholder="Enter offer name"
            className={`${inputStyle} ${errors.name ? errorInputStyle : 'border-gray-300'}`}
          />
          {errors.name && <p className="text-red-500 text-xs mt-1">{errors.name}</p>}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Offer Code *
          </label>
          <div className="flex">
            <input
              type="text"
              value={formData.offer_code}
              onChange={(e) => handleInputChange('offer_code', e.target.value.toUpperCase())}
              placeholder="OFFER2025"
              className={`${inputStyle} rounded-r-none ${errors.offer_code ? errorInputStyle : 'border-gray-300'}`}
            />
            <button
              type="button"
              onClick={generateOfferCode}
              disabled={isGeneratingCode}
              className="px-3 py-2 bg-gray-100 border border-l-0 border-gray-300 rounded-r-lg hover:bg-gray-200 text-sm"
            >
              {isGeneratingCode ? (
                <i className="ri-loader-4-line animate-spin"></i>
              ) : (
                <i className="ri-refresh-line"></i>
              )}
            </button>
          </div>
          {errors.offer_code && <p className="text-red-500 text-xs mt-1">{errors.offer_code}</p>}
        </div>
      </div>

      {/* Offer Type and Value */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Offer Type *
          </label>
          <select
            value={formData.offer_type}
            onChange={(e) => handleInputChange('offer_type', e.target.value)}
            className={`${inputStyle} border-gray-300`}
          >
            {OFFER_TYPE_OPTIONS.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Offer Value {formData.offer_type !== 'FREE_UPGRADE' && '*'}
          </label>
          <div className="relative">
            <input
              type="number"
              value={formData.offer_value}
              onChange={(e) => handleInputChange('offer_value', parseFloat(e.target.value) || 0)}
              placeholder="0"
              min="0"
              step={formData.offer_type === 'PERCENTAGE' ? '1' : '0.01'}
              max={formData.offer_type === 'PERCENTAGE' ? '100' : undefined}
              disabled={formData.offer_type === 'FREE_UPGRADE'}
              className={`${inputStyle} ${errors.offer_value ? errorInputStyle : 'border-gray-300'}`}
            />
            <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
              <span className="text-gray-500 text-sm">
                {formData.offer_type === 'PERCENTAGE' ? '%' : formData.offer_type === 'FIXED_AMOUNT' ? '$' : ''}
              </span>
            </div>
          </div>
          {errors.offer_value && <p className="text-red-500 text-xs mt-1">{errors.offer_value}</p>}
        </div>
      </div>

      {/* Business and Service Type */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Business Type *
          </label>
          <select
            value={formData.business_type}
            onChange={(e) => handleInputChange('business_type', e.target.value)}
            className={`${inputStyle} border-gray-300`}
          >
            {BUSINESS_TYPE_OPTIONS.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Service Type *
          </label>
          <select
            value={formData.service_type}
            onChange={(e) => handleInputChange('service_type', e.target.value)}
            className={`${inputStyle} border-gray-300`}
          >
            {SERVICE_TYPE_OPTIONS.map(option => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Description */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Description *
        </label>
        <textarea
          value={formData.description}
          onChange={(e) => handleInputChange('description', e.target.value)}
          placeholder="Describe the offer..."
          rows={3}
          className={`${inputStyle} ${errors.description ? errorInputStyle : 'border-gray-300'}`}
        />
        {errors.description && <p className="text-red-500 text-xs mt-1">{errors.description}</p>}
      </div>

      {/* Terms and Conditions */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Terms and Conditions
        </label>
        <textarea
          value={formData.terms_and_conditions || ''}
          onChange={(e) => handleInputChange('terms_and_conditions', e.target.value)}
          placeholder="Enter terms and conditions..."
          rows={3}
          className={`${inputStyle} border-gray-300`}
        />
      </div>

      {/* Validity Dates */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Valid From
          </label>
          <input
            type="date"
            value={formData.valid_from || ''}
            onChange={(e) => handleInputChange('valid_from', e.target.value)}
            className={`${inputStyle} border-gray-300`}
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Valid To
          </label>
          <input
            type="date"
            value={formData.valid_to || ''}
            onChange={(e) => handleInputChange('valid_to', e.target.value)}
            className={`${inputStyle} ${errors.valid_to ? errorInputStyle : 'border-gray-300'}`}
          />
          {errors.valid_to && <p className="text-red-500 text-xs mt-1">{errors.valid_to}</p>}
        </div>
      </div>

      {/* Image Upload - Hotel Management Style */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Offer Image
        </label>
        <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors">
          <input
            type="file"
            accept="image/*"
            onChange={handleImageUpload}
            className="hidden"
            id="image-upload"
          />
          <label htmlFor="image-upload" className="cursor-pointer">
            <i className="ri-upload-cloud-2-line text-4xl text-gray-400 mb-2 block"></i>
            <p className="text-sm text-gray-600">
              Click to upload image or drag and drop
            </p>
            <p className="text-xs text-gray-500 mt-1">
              PNG, JPG, GIF up to 10MB
            </p>
          </label>
        </div>

        {/* Image Preview */}
        {(imagePreviewUrl || formData.image) && (
          <div className="mt-4">
            <h4 className="text-sm font-medium text-gray-700 mb-3">Image Preview</h4>
            <div className="relative group inline-block">
              <img
                src={imagePreviewUrl || formData.image || ''}
                alt="Offer preview"
                className="w-32 h-32 object-cover rounded-lg border border-gray-200"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.src = `https://via.placeholder.com/128x128/f3f4f6/6b7280?text=Image+Error`;
                }}
              />
              <button
                type="button"
                onClick={handleImageRemove}
                className="absolute top-2 right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity hover:bg-red-600"
              >
                <i className="ri-close-line text-xs"></i>
              </button>
            </div>
          </div>
        )}

        {/* URL Input as Alternative */}
        <div className="mt-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Or enter image URL
          </label>
          <input
            type="url"
            value={formData.image || ''}
            onChange={(e) => {
              handleInputChange('image', e.target.value);
              if (e.target.value) {
                setImagePreviewUrl(e.target.value);
                setUploadedImage(null);
              }
            }}
            placeholder="https://example.com/offer-image.jpg"
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
          />
        </div>

        {errors.image && (
          <p className="text-red-500 text-sm mt-1">{errors.image}</p>
        )}
      </div>

      {/* User ID (Optional) */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          User ID (Optional)
        </label>
        <input
          type="number"
          value={formData.user_id || ''}
          onChange={(e) => handleInputChange('user_id', e.target.value ? parseInt(e.target.value) : null)}
          placeholder="Enter user ID for user-specific offer"
          className={`${inputStyle} border-gray-300`}
        />
        <p className="text-xs text-gray-500 mt-1">Leave empty for general offers</p>
      </div>

      {/* Form Actions */}
      <div className="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
        >
          Cancel
        </button>
        <button
          type="submit"
          disabled={loading}
          className="px-4 py-2 text-sm font-medium text-white bg-orange-600 border border-transparent rounded-lg hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {loading ? (
            <>
              <i className="ri-loader-4-line animate-spin mr-2"></i>
              {mode === 'edit' ? 'Updating...' : 'Creating...'}
            </>
          ) : (
            <>
              <i className="ri-save-line mr-2"></i>
              {mode === 'edit' ? 'Update Offer' : 'Create Offer'}
            </>
          )}
        </button>
      </div>
    </form>
  );
}
