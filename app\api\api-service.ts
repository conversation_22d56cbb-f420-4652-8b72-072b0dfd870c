import { AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';
import axiosInstance from './axiosInstance';
import axiosInstanceprovider from './axiosInstanceprovider';
import axiosInstancebooking from './axiosInstancebooking';
import axiosInstancetermspolicies from './axiosInstancetermspolicies';
import axiosInstancefaq from './axiosInstancefaq';
import axiosInstancecoupons from './axiosInstancecoupons';
import axiosInstancemarkup from './axiosInstancemarkup';
import axiosInstancevirtual from './axiosInstancevirtual';
import axiosInstancesettings from './axiosInstancesettings';
import axiosInstanceoffers from './axiosInstanceoffers';
import axiosInstancelanguage from './axiosInstancelanguage';

class ApiService {


  //GET 
  async get<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    try {
      const response: AxiosResponse<T> = await axiosInstance.get(url, config);
      return response.data;
    } catch (error: unknown) {
      this.handleError(error);
    }
  }

   async getprovider<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    try {
      const response: AxiosResponse<T> = await axiosInstanceprovider.get(url, config);
      return response.data;
    } catch (error: unknown) {
      this.handleError(error);
    }
  }
   async getbooking<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    try {
      const response: AxiosResponse<T> = await axiosInstancebooking
      .get(url, config);
      return response.data;
    } catch (error: unknown) {
      this.handleError(error);
    }
  }


    async gettermspolicies<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    try {
      const response: AxiosResponse<T> = await axiosInstancetermspolicies
    .get(url, config);
      return response.data;
    } catch (error: unknown) {
      this.handleError(error);
    }
  }
  async getfaq<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    try {
      const response: AxiosResponse<T> = await axiosInstancefaq.get(url, config);
      return response.data;
    } catch (error: unknown) {
      this.handleError(error);
    }
  }

  async getcoupons<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    try {
      const response: AxiosResponse<T> = await axiosInstancecoupons.get(url, config);
      return response.data;
    } catch (error: unknown) {
      this.handleError(error);
    }
  }
   async getmarkup<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    try {
      const response: AxiosResponse<T> = await axiosInstancemarkup.get(url, config);
      return response.data;
    } catch (error: unknown) {
      this.handleError(error);
    }
  }
   async getvirtual<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    try {
      const response: AxiosResponse<T> = await axiosInstancevirtual.get(url, config);
      return response.data;
    } catch (error: unknown) {
      this.handleError(error);
    }
  }
    async getsettings<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    try {
      const response: AxiosResponse<T> = await axiosInstancesettings.get(url, config);
      return response.data;
    } catch (error: unknown) {
      this.handleError(error);
    }
  }

 async getoffers<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    try {
      const response: AxiosResponse<T> = await axiosInstanceoffers.get(url, config);
      return response.data;
    } catch (error: unknown) {
      this.handleError(error);
    }
  }

  async getlanguage<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    try {
      const response: AxiosResponse<T> = await axiosInstancelanguage.get(url, config);
      return response.data;
    } catch (error: unknown) {
      this.handleError(error);
    }
  }











  //POST
  async post<T>(url: string, data?: unknown, config?: AxiosRequestConfig): Promise<T> {
    try {
      const response: AxiosResponse<T> = await axiosInstance.post(url, data, config);
      return response.data;
    } catch (error: unknown) {
      this.handleError(error);
    }
  }

   async postprovider<T>(url: string, data?: unknown, config?: AxiosRequestConfig): Promise<T> {
    try {
      const response: AxiosResponse<T> = await axiosInstanceprovider.post(url, data, config);
      return response.data;
    } catch (error: unknown) {
      this.handleError(error);
    }
  }

   async postbooking<T>(url: string, data?: unknown, config?: AxiosRequestConfig): Promise<T> {
    try {
      const response: AxiosResponse<T> = await axiosInstancebooking.post(url, data, config);
      return response.data;
    } catch (error: unknown) {
      this.handleError(error);
    }
  }


    async posttermspolicies<T>(url: string, data?: unknown, config?: AxiosRequestConfig): Promise<T> {
    try {
      const response: AxiosResponse<T> = await axiosInstancetermspolicies.post(url, data, config);
      return response.data;
    } catch (error: unknown) {
      this.handleError(error);
    }
  }

   async postfaq<T>(url: string, data?: unknown, config?: AxiosRequestConfig): Promise<T> {
    try {
      const response: AxiosResponse<T> = await axiosInstancefaq.post(url, data, config);
      return response.data;
    } catch (error: unknown) {
      this.handleError(error);
    }
  }


   async postcoupons<T>(url: string, data?: unknown, config?: AxiosRequestConfig): Promise<T> {
    try {
      const response: AxiosResponse<T> = await axiosInstancecoupons.post(url, data, config);
      return response.data;
    } catch (error: unknown) {
      this.handleError(error);
    }
  }

    async postmarkup<T>(url: string, data?: unknown, config?: AxiosRequestConfig): Promise<T> {
    try {
      const response: AxiosResponse<T> = await axiosInstancemarkup.post(url, data, config);
      return response.data;
    } catch (error: unknown) {
      this.handleError(error);
    }
  }

   async postvirtual<T>(url: string, data?: unknown, config?: AxiosRequestConfig): Promise<T> {
    try {
      const response: AxiosResponse<T> = await axiosInstancevirtual.post(url, data, config);
      return response.data;
    } catch (error: unknown) {
      this.handleError(error);
    }
  }
   async postsettings<T>(url: string, data?: unknown, config?: AxiosRequestConfig): Promise<T> {
    try {
      const response: AxiosResponse<T> = await axiosInstancesettings.post(url, data, config);
      return response.data;
    } catch (error: unknown) {
      this.handleError(error);
    }
  }
   async postoffers<T>(url: string, data?: unknown, config?: AxiosRequestConfig): Promise<T> {
    try {
      const response: AxiosResponse<T> = await axiosInstanceoffers.post(url, data, config);
      return response.data;
    } catch (error: unknown) {
      this.handleError(error);
    }
  }
    async postlanguage<T>(url: string, data?: unknown, config?: AxiosRequestConfig): Promise<T> {
    try {
      const response: AxiosResponse<T> = await axiosInstancelanguage.post(url, data, config);
      return response.data;
    } catch (error: unknown) {
      this.handleError(error);
    }
  }













  //PUT
  async put<T>(url: string, data?: unknown, config?: AxiosRequestConfig): Promise<T> {
    try {
      const response: AxiosResponse<T> = await axiosInstance.put(url, data, config);
      return response.data;
    } catch (error: unknown) {
      this.handleError(error);
    }
  }

    async putprovider<T>(url: string, data?: unknown, config?: AxiosRequestConfig): Promise<T> {
    try {
      const response: AxiosResponse<T> = await axiosInstanceprovider.put(url, data, config);
      return response.data;
    } catch (error: unknown) {
      this.handleError(error);
    }
  }

    async putbooking<T>(url: string, data?: unknown, config?: AxiosRequestConfig): Promise<T> {
    try {
      const response: AxiosResponse<T> = await axiosInstancebooking.put(url, data, config);
      return response.data;
    } catch (error: unknown) {
      this.handleError(error);
    }
  }
    async puttermspolicies<T>(url: string, data?: unknown, config?: AxiosRequestConfig): Promise<T> {
    try {
      const response: AxiosResponse<T> = await axiosInstancetermspolicies.put(url, data, config);
      return response.data;
    } catch (error: unknown) {
      this.handleError(error);
    }
  }

   async putfaq<T>(url: string, data?: unknown, config?: AxiosRequestConfig): Promise<T> {
    try {
      const response: AxiosResponse<T> = await axiosInstancefaq.put(url, data, config);
      return response.data;
    } catch (error: unknown) {
      this.handleError(error);
    }
  }

   async putcoupons<T>(url: string, data?: unknown, config?: AxiosRequestConfig): Promise<T> {
    try {
      const response: AxiosResponse<T> = await axiosInstancecoupons.put(url, data, config);
      return response.data;
    } catch (error: unknown) {
      this.handleError(error);
    }
  }

    async putmarkup<T>(url: string, data?: unknown, config?: AxiosRequestConfig): Promise<T> {
    try {
      const response: AxiosResponse<T> = await axiosInstancemarkup.put(url, data, config);
      return response.data;
    } catch (error: unknown) {
      this.handleError(error);
    }
  }

  async putvirtual<T>(url: string, data?: unknown, config?: AxiosRequestConfig): Promise<T> {
    try {
      const response: AxiosResponse<T> = await axiosInstancevirtual.put(url, data, config);
      return response.data;
    } catch (error: unknown) {
      this.handleError(error);
    }
  }
  async putsettings<T>(url: string, data?: unknown, config?: AxiosRequestConfig): Promise<T> {
    try {
      const response: AxiosResponse<T> = await axiosInstancesettings.put(url, data, config);
      return response.data;
    } catch (error: unknown) {
      this.handleError(error);
    }
  }
   async putoffers<T>(url: string, data?: unknown, config?: AxiosRequestConfig): Promise<T> {
    try {
      const response: AxiosResponse<T> = await axiosInstanceoffers.put(url, data, config);
      return response.data;
    } catch (error: unknown) {
      this.handleError(error);
    }
  }
  async putlanguage<T>(url: string, data?: unknown, config?: AxiosRequestConfig): Promise<T> {
    try {
      const response: AxiosResponse<T> = await axiosInstancelanguage.put(url, data, config);
      return response.data;
    } catch (error: unknown) {
      this.handleError(error);
    }
  }






  //DELETE
  async delete<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    try {
      const response: AxiosResponse<T> = await axiosInstance.delete(url, config);
      return response.data;
    } catch (error: unknown) {
      this.handleError(error);
    } 
  }

    async deleteprovider<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    try {
      const response: AxiosResponse<T> = await axiosInstanceprovider.delete(url, config);
      return response.data;
    } catch (error: unknown) {
      this.handleError(error);
    }
  }
   async deletebooking<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    try {
      const response: AxiosResponse<T> = await axiosInstancebooking.delete(url, config);
      return response.data;
    } catch (error: unknown) {
      this.handleError(error);
    }
  }
   async deletetermspolicies<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    try {
      const response: AxiosResponse<T> = await axiosInstancetermspolicies.delete(url, config);
      return response.data;
    } catch (error: unknown) {
      this.handleError(error);
    }
  }
   async deletefaq<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    try {
      const response: AxiosResponse<T> = await axiosInstancefaq.delete(url, config);
      return response.data;
    } catch (error: unknown) {
      this.handleError(error);
    }
  }
   async deletemarkup<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    try {
      const response: AxiosResponse<T> = await axiosInstancemarkup.delete(url, config);
      return response.data;
    } catch (error: unknown) {
      this.handleError(error);
    }
  }

   async deletevirtual<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    try {
      const response: AxiosResponse<T> = await axiosInstancevirtual.delete(url, config);
      return response.data;
    } catch (error: unknown) {
      this.handleError(error);
    }
  }
   async deletesettings<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    try {
      const response: AxiosResponse<T> = await axiosInstancesettings.delete(url, config);
      return response.data;
    } catch (error: unknown) {
      this.handleError(error);
    }
  }
   async deletecoupons<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    try {
      const response: AxiosResponse<T> = await axiosInstancecoupons.delete(url, config);
      return response.data;
    } catch (error: unknown) {
      this.handleError(error);
    }
  }
  async deleteoffers<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    try {
      const response: AxiosResponse<T> = await axiosInstanceoffers.delete(url, config);
      return response.data;
    } catch (error: unknown) {
      this.handleError(error);
    }
  }
  async deletelanguage<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    try {
      const response: AxiosResponse<T> = await axiosInstancelanguage.delete(url, config);
      return response.data;
    } catch (error: unknown) {
      this.handleError(error);
    } 
  }













  
  //POST FormData
  async postFormData<T>(url: string, formData: FormData, config?: AxiosRequestConfig): Promise<T> {
    try {
      if (!url.includes('formData/')) {
        url = `formData/${url}`;
      }

      const response: AxiosResponse<T> = await axiosInstance.post(url, formData, {
        ...config,
        headers: {
          ...(config?.headers || {}),
          'Content-Type': 'multipart/form-data',
        },
      });

      return response.data;
    } catch (error: unknown) {
      this.handleError(error);
    }
  }
    async postFormDatasettings<T>(url: string, formData: FormData, config?: AxiosRequestConfig): Promise<T> {
    try {
      if (!url.includes('formData/')) {
        url = `formData/${url}`;
      }

      const response: AxiosResponse<T> = await axiosInstancesettings.post(url, formData, {
        ...config,
        headers: {
          ...(config?.headers || {}),
          'Content-Type': 'multipart/form-data',
        },
      });

      return response.data;
    } catch (error: unknown) {
      this.handleError(error);
    }
  }
    async postFormDatavirtual<T>(url: string, formData: FormData, config?: AxiosRequestConfig): Promise<T> {
    try {
      if (!url.includes('formData/')) {
        url = `formData/${url}`;
      }

      const response: AxiosResponse<T> = await axiosInstancevirtual.post(url, formData, {
        ...config,
        headers: {
          ...(config?.headers || {}),
          'Content-Type': 'multipart/form-data',
        },
      });

      return response.data;
    } catch (error: unknown) {
      this.handleError(error);
    }
  }
    async postFormDataoffers<T>(url: string, formData: FormData, config?: AxiosRequestConfig): Promise<T> {
    try {
      if (!url.includes('formData/')) {
        url = `formData/${url}`;
      }

      const response: AxiosResponse<T> = await axiosInstance.post(url, formData, {
        ...config,
        headers: {
          ...(config?.headers || {}),
          'Content-Type': 'multipart/form-data',
        },
      });

      return response.data;
    } catch (error: unknown) {
      this.handleError(error);
    }
  }
    async postFormDatalanguage<T>(url: string, formData: FormData, config?: AxiosRequestConfig): Promise<T> {
    try {
      if (!url.includes('formData/')) {
        url = `formData/${url}`;
      }

      const response: AxiosResponse<T> = await axiosInstancelanguage.post(url, formData, {
        ...config,
        headers: {
          ...(config?.headers || {}),
          'Content-Type': 'multipart/form-data',
        },
      });

      return response.data;
    } catch (error: unknown) {
      this.handleError(error);
    }
  }
 




  //PUT FormData

  async putFormData<T>(url: string, formData: FormData, config?: AxiosRequestConfig): Promise<T> {
    try {
      if (!url.includes('formData/')) {
        url = `formData/${url}`;
      }

      const response: AxiosResponse<T> = await axiosInstance.put(url, formData, {
        ...config,
        headers: {
          ...(config?.headers || {}),
          'Content-Type': 'multipart/form-data',
        },
      });

      return response.data;
    } catch (error: unknown) {
      this.handleError(error);
    }
  }
   async putFormDatasettings<T>(url: string, formData: FormData, config?: AxiosRequestConfig): Promise<T> {
    try {
      if (!url.includes('formData/')) {
        url = `formData/${url}`;
      }

      const response: AxiosResponse<T> = await axiosInstancesettings.put(url, formData, {
        ...config,
        headers: {
          ...(config?.headers || {}),
          'Content-Type': 'multipart/form-data',
        },
      });

      return response.data;
    } catch (error: unknown) {
      this.handleError(error);
    }
  }
   async putFormDatavirtual<T>(url: string, formData: FormData, config?: AxiosRequestConfig): Promise<T> {
    try {
      if (!url.includes('formData/')) {
        url = `formData/${url}`;
      }

      const response: AxiosResponse<T> = await axiosInstancevirtual.put(url, formData, {
        ...config,
        headers: {
          ...(config?.headers || {}),
          'Content-Type': 'multipart/form-data',
        },
      });

      return response.data;
    } catch (error: unknown) {
      this.handleError(error);
    }
  }
  async putFormDataoffers<T>(url: string, formData: FormData, config?: AxiosRequestConfig): Promise<T> {
    try {
      if (!url.includes('formData/')) {
        url = `formData/${url}`;
      }

      const response: AxiosResponse<T> = await axiosInstance.put(url, formData, {
        ...config,
        headers: {
          ...(config?.headers || {}),
          'Content-Type': 'multipart/form-data',
        },
      });

      return response.data;
    } catch (error: unknown) {
      this.handleError(error);
    }
  }

    async putFormDatalanguage<T>(url: string, formData: FormData, config?: AxiosRequestConfig): Promise<T> {
    try {
      if (!url.includes('formData/')) {
        url = `formData/${url}`;
      }

      const response: AxiosResponse<T> = await axiosInstancelanguage.put(url, formData, {
        ...config,
        headers: {
          ...(config?.headers || {}),
          'Content-Type': 'multipart/form-data',
        },
      });

      return response.data;
    } catch (error: unknown) {
      this.handleError(error);
    }
  }






  // 👇 Safe, typed error handling
  private handleError(error: unknown): never {
    if (error instanceof AxiosError) {
      console.error('API Error:', error.response?.data || error.message);
      throw error;
    }

    // Unknown type fallback
    console.error('Unexpected error:', error);
    throw new Error('An unexpected error occurred');
  }
}

const apiService = new ApiService();
export default apiService;
