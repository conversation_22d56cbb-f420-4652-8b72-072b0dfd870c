'use client';

import React from 'react';
import { FaqFilters, LANGUAGE_OPTIONS, SERVICE_OPTIONS, PAGE_TYPE_OPTIONS } from '../faq.model';

interface FaqFiltersProps {
  filters: FaqFilters;
  onFiltersChange: (filters: FaqFilters) => void;
  onClearFilters: () => void;
}

export default function FaqFiltersComponent({ 
  filters, 
  onFiltersChange, 
  onClearFilters 
}: FaqFiltersProps) {
  
  const handleFilterChange = (field: keyof FaqFilters, value: string) => {
    onFiltersChange({
      ...filters,
      [field]: value
    });
  };

  const handleDateRangeChange = (field: 'startDate' | 'endDate', value: string) => {
    onFiltersChange({
      ...filters,
      dateRange: {
        ...filters.dateRange,
        [field]: value
      }
    });
  };

  const hasActiveFilters = 
    filters.search || 
    filters.language || 
    filters.language_code || 
    filters.service || 
    filters.page_type ||
    filters.dateRange.startDate || 
    filters.dateRange.endDate;

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-gray-900">Filters</h3>
        {hasActiveFilters && (
          <button
            onClick={onClearFilters}
            className="text-sm text-blue-600 hover:text-blue-800 font-medium"
          >
            Clear All
          </button>
        )}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
        {/* Search */}
        <div className="lg:col-span-2">
          <label htmlFor="search" className="block text-sm font-medium text-gray-700 mb-2">
            Search
          </label>
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <i className="ri-search-line text-gray-400"></i>
            </div>
            <input
              type="text"
              id="search"
              value={filters.search}
              onChange={(e) => handleFilterChange('search', e.target.value)}
              className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Search questions or answers..."
            />
          </div>
        </div>

        {/* Language */}
        <div>
          <label htmlFor="language" className="block text-sm font-medium text-gray-700 mb-2">
            Language
          </label>
          <select
            id="language"
            value={filters.language}
            onChange={(e) => handleFilterChange('language', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">All Languages</option>
            {LANGUAGE_OPTIONS.map((option) => (
              <option key={option.code} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>

        {/* Service */}
        <div>
          <label htmlFor="service" className="block text-sm font-medium text-gray-700 mb-2">
            Service
          </label>
          <select
            id="service"
            value={filters.service}
            onChange={(e) => handleFilterChange('service', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">All Services</option>
            {SERVICE_OPTIONS.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>

        {/* Page Type */}
        <div>
          <label htmlFor="page_type" className="block text-sm font-medium text-gray-700 mb-2">
            Page Type
          </label>
          <select
            id="page_type"
            value={filters.page_type}
            onChange={(e) => handleFilterChange('page_type', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">All Page Types</option>
            {PAGE_TYPE_OPTIONS.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>

        {/* Date Range */}
        <div>
          <label htmlFor="start_date" className="block text-sm font-medium text-gray-700 mb-2">
            Start Date
          </label>
          <input
            type="date"
            id="start_date"
            value={filters.dateRange.startDate}
            onChange={(e) => handleDateRangeChange('startDate', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>

        <div>
          <label htmlFor="end_date" className="block text-sm font-medium text-gray-700 mb-2">
            End Date
          </label>
          <input
            type="date"
            id="end_date"
            value={filters.dateRange.endDate}
            onChange={(e) => handleDateRangeChange('endDate', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
      </div>

      {/* Active Filters Display */}
      {hasActiveFilters && (
        <div className="mt-4 pt-4 border-t border-gray-200">
          <div className="flex flex-wrap gap-2">
            {filters.search && (
              <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                Search: "{filters.search}"
                <button
                  onClick={() => handleFilterChange('search', '')}
                  className="ml-2 text-blue-600 hover:text-blue-800"
                >
                  <i className="ri-close-line"></i>
                </button>
              </span>
            )}
            {filters.language && (
              <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                Language: {filters.language}
                <button
                  onClick={() => handleFilterChange('language', '')}
                  className="ml-2 text-purple-600 hover:text-purple-800"
                >
                  <i className="ri-close-line"></i>
                </button>
              </span>
            )}
            {filters.service && (
              <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                Service: {filters.service}
                <button
                  onClick={() => handleFilterChange('service', '')}
                  className="ml-2 text-green-600 hover:text-green-800"
                >
                  <i className="ri-close-line"></i>
                </button>
              </span>
            )}
            {filters.page_type && (
              <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                Page Type: {filters.page_type}
                <button
                  onClick={() => handleFilterChange('page_type', '')}
                  className="ml-2 text-yellow-600 hover:text-yellow-800"
                >
                  <i className="ri-close-line"></i>
                </button>
              </span>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
