'use client';

import { useState } from 'react';
import { 
  OfferFilters, 
  OFFER_TYPE_OPTIONS, 
  BUSINESS_TYPE_OPTIONS, 
  SERVICE_TYPE_OPTIONS,
  OFFER_STATUS_OPTIONS 
} from '../offer.model';

interface OfferFiltersProps {
  filters: OfferFilters;
  onFiltersChange: (filters: OfferFilters) => void;
  onClearFilters?: () => void;
}

export default function OfferFiltersComponent({ 
  filters, 
  onFiltersChange, 
  onClearFilters 
}: OfferFiltersProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  const handleFilterChange = (field: keyof OfferFilters, value: string) => {
    onFiltersChange({
      ...filters,
      [field]: value
    });
  };

  const clearAllFilters = () => {
    if (onClearFilters) {
      onClearFilters();
    } else {
      onFiltersChange({
        search: '',
        offer_type: '',
        business_type: '',
        service_type: '',
        status: '',
        valid_from: '',
        valid_to: ''
      });
    }
  };

  const hasActiveFilters = () => {
    return filters.search || 
           filters.offer_type || 
           filters.business_type || 
           filters.service_type || 
           filters.status || 
           filters.valid_from || 
           filters.valid_to;
  };

  const getActiveFilterCount = () => {
    let count = 0;
    if (filters.search) count++;
    if (filters.offer_type) count++;
    if (filters.business_type) count++;
    if (filters.service_type) count++;
    if (filters.status) count++;
    if (filters.valid_from) count++;
    if (filters.valid_to) count++;
    return count;
  };

  return (
    <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
      {/* Filter Header */}
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <h3 className="text-lg font-medium text-gray-900">Filters</h3>
            {hasActiveFilters() && (
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                {getActiveFilterCount()} active
              </span>
            )}
          </div>
          <div className="flex items-center space-x-2">
            {hasActiveFilters() && (
              <button
                onClick={clearAllFilters}
                className="text-sm text-gray-500 hover:text-gray-700 px-3 py-1 rounded-md hover:bg-gray-100"
              >
                <i className="ri-close-line mr-1"></i>
                Clear all
              </button>
            )}
            <button
              onClick={() => setIsExpanded(!isExpanded)}
              className="text-gray-400 hover:text-gray-600 p-1 rounded-md hover:bg-gray-100"
            >
              <i className={`ri-arrow-${isExpanded ? 'up' : 'down'}-s-line`}></i>
            </button>
          </div>
        </div>
      </div>

      {/* Search Bar - Always Visible */}
      <div className="px-6 py-4">
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <i className="ri-search-line text-gray-400"></i>
          </div>
          <input
            type="text"
            value={filters.search}
            onChange={(e) => handleFilterChange('search', e.target.value)}
            placeholder="Search offers by name, code, or description..."
            className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 text-sm"
          />
          {filters.search && (
            <button
              onClick={() => handleFilterChange('search', '')}
              className="absolute inset-y-0 right-0 pr-3 flex items-center"
            >
              <i className="ri-close-line text-gray-400 hover:text-gray-600"></i>
            </button>
          )}
        </div>
      </div>

      {/* Advanced Filters - Collapsible */}
      {isExpanded && (
        <div className="px-6 pb-6 border-t border-gray-100">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4 mt-4">
            {/* Offer Type Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Offer Type
              </label>
              <select
                value={filters.offer_type || ''}
                onChange={(e) => handleFilterChange('offer_type', e.target.value)}
                className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 text-sm"
              >
                <option value="">All Types</option>
                {OFFER_TYPE_OPTIONS.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            {/* Business Type Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Business Type
              </label>
              <select
                value={filters.business_type || ''}
                onChange={(e) => handleFilterChange('business_type', e.target.value)}
                className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 text-sm"
              >
                <option value="">All Business Types</option>
                {BUSINESS_TYPE_OPTIONS.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            {/* Service Type Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Service Type
              </label>
              <select
                value={filters.service_type || ''}
                onChange={(e) => handleFilterChange('service_type', e.target.value)}
                className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 text-sm"
              >
                <option value="">All Services</option>
                {SERVICE_TYPE_OPTIONS.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            {/* Status Filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Status
              </label>
              <select
                value={filters.status || ''}
                onChange={(e) => handleFilterChange('status', e.target.value)}
                className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 text-sm"
              >
                <option value="">All Statuses</option>
                {OFFER_STATUS_OPTIONS.map(option => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>

            {/* Date Range Filters */}
            <div className="md:col-span-2 lg:col-span-1">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Valid From
              </label>
              <input
                type="date"
                value={filters.valid_from || ''}
                onChange={(e) => handleFilterChange('valid_from', e.target.value)}
                className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 text-sm"
              />
            </div>
          </div>

          {/* Second row for Valid To date */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4 mt-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Valid To
              </label>
              <input
                type="date"
                value={filters.valid_to || ''}
                onChange={(e) => handleFilterChange('valid_to', e.target.value)}
                className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-orange-500 text-sm"
              />
            </div>
          </div>

          {/* Quick Filter Buttons */}
          <div className="mt-6 pt-4 border-t border-gray-100">
            <div className="flex flex-wrap gap-2">
              <button
                onClick={() => handleFilterChange('status', 'ACTIVE')}
                className={`px-3 py-1 rounded-full text-xs font-medium border transition-colors ${
                  filters.status === 'ACTIVE'
                    ? 'bg-green-100 text-green-800 border-green-300'
                    : 'bg-white text-gray-600 border-gray-300 hover:bg-gray-50'
                }`}
              >
                <i className="ri-check-line mr-1"></i>
                Active Only
              </button>
              <button
                onClick={() => handleFilterChange('status', 'EXPIRED')}
                className={`px-3 py-1 rounded-full text-xs font-medium border transition-colors ${
                  filters.status === 'EXPIRED'
                    ? 'bg-red-100 text-red-800 border-red-300'
                    : 'bg-white text-gray-600 border-gray-300 hover:bg-gray-50'
                }`}
              >
                <i className="ri-time-line mr-1"></i>
                Expired Only
              </button>
              <button
                onClick={() => handleFilterChange('offer_type', 'PERCENTAGE')}
                className={`px-3 py-1 rounded-full text-xs font-medium border transition-colors ${
                  filters.offer_type === 'PERCENTAGE'
                    ? 'bg-blue-100 text-blue-800 border-blue-300'
                    : 'bg-white text-gray-600 border-gray-300 hover:bg-gray-50'
                }`}
              >
                <i className="ri-percent-line mr-1"></i>
                Percentage
              </button>
              <button
                onClick={() => handleFilterChange('business_type', 'B2C')}
                className={`px-3 py-1 rounded-full text-xs font-medium border transition-colors ${
                  filters.business_type === 'B2C'
                    ? 'bg-purple-100 text-purple-800 border-purple-300'
                    : 'bg-white text-gray-600 border-gray-300 hover:bg-gray-50'
                }`}
              >
                <i className="ri-user-line mr-1"></i>
                B2C
              </button>
              <button
                onClick={() => handleFilterChange('service_type', 'HOTEL')}
                className={`px-3 py-1 rounded-full text-xs font-medium border transition-colors ${
                  filters.service_type === 'HOTEL'
                    ? 'bg-orange-100 text-orange-800 border-orange-300'
                    : 'bg-white text-gray-600 border-gray-300 hover:bg-gray-50'
                }`}
              >
                <i className="ri-hotel-line mr-1"></i>
                Hotels
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
