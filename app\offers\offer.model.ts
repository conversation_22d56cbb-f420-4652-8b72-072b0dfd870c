// Offer Management Models and Types

// Enums for offer properties
export type OfferType = 'PERCENTAGE' | 'FIXED_AMOUNT' | 'BUY_ONE_GET_ONE' | 'FREE_UPGRADE';
export type BusinessType = 'B2B' | 'B2C' | 'BOTH';
export type ServiceType = 'HOTEL' | 'FLIGHT' | 'PACKAGE' | 'ALL';
export type OfferStatus = 'ACTIVE' | 'INACTIVE' | 'EXPIRED' | 'DRAFT';

// Main Offer interface - matches API response structure
export interface Offer {
  id: number;
  name: string;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
  created_by: string;
  updated_by: string;
  offer_type: OfferType;
  offer_value: number;
  business_type: BusinessType;
  service_type: ServiceType;
  user_id: number | null;
  offer_code: string;
  image: string | null;
  description: string;
  terms_and_conditions: string | null;
  valid_from: string | null;
  valid_to: string | null;
  flights: any | null;
  hotels: any | null;
}

// Form data interface for create/update operations
export interface OfferFormData {
  name: string;
  offer_type: OfferType;
  offer_value: number;
  business_type: BusinessType;
  service_type: ServiceType;
  offer_code: string;
  description: string;
  user_id?: number | null;
  image?: string | null;
  terms_and_conditions?: string | null;
  valid_from?: string | null;
  valid_to?: string | null;
}

// Create request interface
export interface CreateOfferRequest {
  name: string;
  offer_type: OfferType;
  offer_value: number;
  business_type: BusinessType;
  service_type: ServiceType;
  user_id?: number | null;
  offer_code: string;
  image?: string | null;
  description: string;
  terms_and_conditions?: string | null;
  valid_from?: string | null;
  valid_to?: string | null;
}

// Update request interface (all fields optional)
export interface UpdateOfferRequest {
  name?: string;
  offer_type?: OfferType;
  offer_value?: number;
  business_type?: BusinessType;
  service_type?: ServiceType;
  user_id?: number | null;
  offer_code?: string;
  image?: string | null;
  description?: string;
  terms_and_conditions?: string | null;
  valid_from?: string | null;
  valid_to?: string | null;
}

// Filters interface for list operations
export interface OfferFilters {
  search: string;
  offer_type?: OfferType | '';
  business_type?: BusinessType | '';
  service_type?: ServiceType | '';
  status?: OfferStatus | '';
  valid_from?: string;
  valid_to?: string;
}

// API Response interface for list operations
export interface OfferListResponse {
  items: Offer[];
  pagination: {
    current_page: number;
    total_pages: number;
    page_size: number;
    total_items: number;
    has_next: boolean;
    has_previous: boolean;
  };
}

// Statistics interface
export interface OfferStats {
  totalOffers: number;
  activeOffers: number;
  expiredOffers: number;
  draftOffers: number;
  lastUpdated: string;
}

// Default filter values
export const getDefaultOfferFilters = (): OfferFilters => ({
  search: '',
  offer_type: '',
  business_type: '',
  service_type: '',
  status: '',
  valid_from: '',
  valid_to: ''
});

// Offer type options for dropdowns
export const OFFER_TYPE_OPTIONS = [
  { value: 'PERCENTAGE', label: 'Percentage Discount' },
  { value: 'FIXED_AMOUNT', label: 'Fixed Amount' },
  { value: 'BUY_ONE_GET_ONE', label: 'Buy One Get One' },
  { value: 'FREE_UPGRADE', label: 'Free Upgrade' }
] as const;

// Business type options for dropdowns
export const BUSINESS_TYPE_OPTIONS = [
  { value: 'B2B', label: 'Business to Business' },
  { value: 'B2C', label: 'Business to Consumer' },
  { value: 'BOTH', label: 'Both B2B & B2C' }
] as const;

// Service type options for dropdowns
export const SERVICE_TYPE_OPTIONS = [
  { value: 'HOTEL', label: 'Hotel Bookings' },
  { value: 'FLIGHT', label: 'Flight Bookings' },
  { value: 'PACKAGE', label: 'Travel Packages' },
  { value: 'ALL', label: 'All Services' }
] as const;

// Status options for dropdowns
export const OFFER_STATUS_OPTIONS = [
  { value: 'ACTIVE', label: 'Active' },
  { value: 'INACTIVE', label: 'Inactive' },
  { value: 'EXPIRED', label: 'Expired' },
  { value: 'DRAFT', label: 'Draft' }
] as const;

// Utility functions
export const OfferUtils = {
  // Format offer value based on type
  formatOfferValue: (value: number, type: OfferType): string => {
    switch (type) {
      case 'PERCENTAGE':
        return `${value}%`;
      case 'FIXED_AMOUNT':
        return `$${value.toFixed(2)}`;
      case 'BUY_ONE_GET_ONE':
        return 'BOGO';
      case 'FREE_UPGRADE':
        return 'Free Upgrade';
      default:
        return value.toString();
    }
  },

  // Get offer status based on dates
  getOfferStatus: (offer: Offer): OfferStatus => {
    const now = new Date();
    const validFrom = offer.valid_from ? new Date(offer.valid_from) : null;
    const validTo = offer.valid_to ? new Date(offer.valid_to) : null;

    if (validTo && validTo < now) {
      return 'EXPIRED';
    }
    if (validFrom && validFrom > now) {
      return 'DRAFT';
    }
    return 'ACTIVE';
  },

  // Check if offer is currently valid
  isValidNow: (offer: Offer): boolean => {
    const now = new Date();
    const validFrom = offer.valid_from ? new Date(offer.valid_from) : null;
    const validTo = offer.valid_to ? new Date(offer.valid_to) : null;

    if (validFrom && validFrom > now) return false;
    if (validTo && validTo < now) return false;
    return true;
  },

  // Format date for display
  formatDate: (dateString: string | null): string => {
    if (!dateString) return 'No expiry';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  },

  // Generate random offer code
  generateOfferCode: (prefix: string = ''): string => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = prefix;
    for (let i = 0; i < (8 - prefix.length); i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  },

  // Validate offer code format
  isValidOfferCode: (code: string): boolean => {
    return /^[A-Z0-9]{4,12}$/.test(code);
  },

  // Get type label
  getTypeLabel: (type: OfferType): string => {
    const option = OFFER_TYPE_OPTIONS.find(opt => opt.value === type);
    return option?.label || type;
  },

  // Get business type label
  getBusinessTypeLabel: (type: BusinessType): string => {
    const option = BUSINESS_TYPE_OPTIONS.find(opt => opt.value === type);
    return option?.label || type;
  },

  // Get service type label
  getServiceTypeLabel: (type: ServiceType): string => {
    const option = SERVICE_TYPE_OPTIONS.find(opt => opt.value === type);
    return option?.label || type;
  }
};

export default Offer;
