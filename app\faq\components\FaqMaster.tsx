'use client';

import { useState, useEffect } from 'react';
import { Faq, FaqFilters, FaqStats, FaqFormData, getDefaultFaqFilters } from '../faq.model';
import FaqService from '../faq.service';
import FaqStatsComponent from './FaqStats';
import FaqList from './FaqList';
import FaqAddEdit from './FaqAddEdit';
import FaqView from './FaqView';
import FaqFiltersComponent from './FaqFilters';
import Modal from '../../components/ui/Modal';

export default function FaqMaster() {
  // State management
  const [faqs, setFaqs] = useState<Faq[]>([]);
  const [filteredFaqs, setFilteredFaqs] = useState<Faq[]>([]);
  const [stats, setStats] = useState<FaqStats>({
    totalFaqs: 0,
    totalLanguages: 0,
    totalServices: 0,
    lastUpdated: new Date().toISOString()
  });
  const [filters, setFilters] = useState<FaqFilters>(getDefaultFaqFilters());
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Modal states
  const [isAddEditOpen, setIsAddEditOpen] = useState(false);
  const [isViewOpen, setIsViewOpen] = useState(false);
  const [selectedFaq, setSelectedFaq] = useState<Faq | null>(null);
  const [formMode, setFormMode] = useState<'add' | 'edit'>('add');

  // Pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  // Load data on component mount
  useEffect(() => {
    loadFaqs();
    loadStats();
  }, []);

  // Apply filters when filters or faqs change
  useEffect(() => {
    applyFilters();
  }, [faqs, filters]);

  // Load FAQs
  const loadFaqs = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await FaqService.getAllFaqs();
      setFaqs(data);
    } catch (error: any) {
      setError(error.message || 'Failed to load FAQs');
      console.error('Error loading FAQs:', error);
    } finally {
      setLoading(false);
    }
  };

  // Load statistics
  const loadStats = async () => {
    try {
      const statsData = await FaqService.getFaqStats();
      setStats(statsData);
    } catch (error: any) {
      console.error('Error loading FAQ stats:', error);
    }
  };

  // Apply filters to FAQs
  const applyFilters = () => {
    let filtered = [...faqs];

    // Search filter
    if (filters.search) {
      const searchTerm = filters.search.toLowerCase();
      filtered = filtered.filter(faq =>
        faq.question.toLowerCase().includes(searchTerm) ||
        faq.answer.toLowerCase().includes(searchTerm)
      );
    }

    // Language filter
    if (filters.language) {
      filtered = filtered.filter(faq => faq.language === filters.language);
    }

    // Language code filter
    if (filters.language_code) {
      filtered = filtered.filter(faq => faq.language_code === filters.language_code);
    }

    // Service filter
    if (filters.service) {
      filtered = filtered.filter(faq => faq.service === filters.service);
    }

    // Page type filter
    if (filters.page_type) {
      filtered = filtered.filter(faq => faq.page_type === filters.page_type);
    }

    // Date range filter
    if (filters.dateRange.startDate) {
      filtered = filtered.filter(faq => 
        new Date(faq.CreatedAt) >= new Date(filters.dateRange.startDate)
      );
    }

    if (filters.dateRange.endDate) {
      filtered = filtered.filter(faq => 
        new Date(faq.CreatedAt) <= new Date(filters.dateRange.endDate)
      );
    }

    setFilteredFaqs(filtered);
    setCurrentPage(1); // Reset to first page when filters change
  };

  // Handle filter changes
  const handleFiltersChange = (newFilters: FaqFilters) => {
    setFilters(newFilters);
  };

  // Clear all filters
  const handleClearFilters = () => {
    setFilters(getDefaultFaqFilters());
  };

  // Handle add new FAQ
  const handleAddNew = () => {
    setSelectedFaq(null);
    setFormMode('add');
    setIsAddEditOpen(true);
  };

  // Handle edit FAQ
  const handleEditFaq = (faq: Faq) => {
    setSelectedFaq(faq);
    setFormMode('edit');
    setIsAddEditOpen(true);
  };

  // Handle view FAQ
  const handleViewFaq = (faq: Faq) => {
    setSelectedFaq(faq);
    setIsViewOpen(true);
  };

  // Handle delete FAQ
  const handleDeleteFaq = async (id: number) => {
    if (!confirm('Are you sure you want to delete this FAQ?')) {
      return;
    }

    try {
      await FaqService.deleteFaq(id);
      await loadFaqs();
      await loadStats();
    } catch (error: any) {
      alert(error.message || 'Failed to delete FAQ');
    }
  };

  // Handle save FAQ
  const handleSaveFaq = async (formData: FaqFormData) => {
    try {
      if (formMode === 'add') {
        await FaqService.createFaq(formData);
      } else if (selectedFaq) {
        await FaqService.updateFaq(selectedFaq.ID, formData);
      }
      
      setIsAddEditOpen(false);
      setSelectedFaq(null);
      await loadFaqs();
      await loadStats();
    } catch (error: any) {
      throw error; // Re-throw to be handled by the form component
    }
  };

  // Handle form cancel
  const handleFormCancel = () => {
    setIsAddEditOpen(false);
    setSelectedFaq(null);
  };

  // Handle view close
  const handleViewClose = () => {
    setIsViewOpen(false);
    setSelectedFaq(null);
  };

  // Handle view edit
  const handleViewEdit = (faq: Faq) => {
    setIsViewOpen(false);
    handleEditFaq(faq);
  };

  // Handle view related FAQ
  const handleViewRelated = (faq: Faq) => {
    setSelectedFaq(faq);
    // Keep the view modal open to show the new FAQ
  };

  // Pagination calculations
  const totalItems = filteredFaqs.length;
  const totalPages = Math.ceil(totalItems / pageSize);
  const startIndex = (currentPage - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const paginatedFaqs = filteredFaqs.slice(startIndex, endIndex);

  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Handle page size change
  const handlePageSizeChange = (newPageSize: number) => {
    setPageSize(newPageSize);
    setCurrentPage(1);
  };

  return (
    <div className="space-y-6">
      {/* FAQ Statistics */}
      <FaqStatsComponent stats={stats} loading={loading} />

      {/* Header with Actions */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">FAQ Management</h2>
          <p className="text-gray-600">
            Manage frequently asked questions and answers
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <div className="bg-blue-50 px-4 py-2 rounded-lg">
            <span className="text-sm font-medium text-blue-700">
              Total: {totalItems} FAQ{totalItems !== 1 ? 's' : ''}
            </span>
          </div>
          <div className="bg-purple-50 px-4 py-2 rounded-lg">
            <span className="text-sm font-medium text-purple-700">
              Languages: {stats.totalLanguages}
            </span>
          </div>
          <button
            onClick={handleAddNew}
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors shadow-sm"
          >
            <i className="ri-add-line mr-2"></i>
            Add FAQ
          </button>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex">
            <i className="ri-error-warning-line text-red-400 mr-3 mt-0.5"></i>
            <div>
              <h3 className="text-sm font-medium text-red-800">Error</h3>
              <p className="text-sm text-red-700 mt-1">{error}</p>
              <button
                onClick={loadFaqs}
                className="text-sm text-red-600 hover:text-red-800 font-medium mt-2"
              >
                Try Again
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Filters */}
      <FaqFiltersComponent
        filters={filters}
        onFiltersChange={handleFiltersChange}
        onClearFilters={handleClearFilters}
      />

      {/* FAQ List */}
      <FaqList
        faqs={paginatedFaqs}
        onEdit={handleEditFaq}
        onView={handleViewFaq}
        onDelete={handleDeleteFaq}
        loading={loading}
        currentPage={currentPage}
        totalPages={totalPages}
        pageSize={pageSize}
        totalItems={totalItems}
        onPageChange={handlePageChange}
        onPageSizeChange={handlePageSizeChange}
      />

      {/* Add/Edit Modal */}
      <Modal
        isOpen={isAddEditOpen}
        onClose={handleFormCancel}
        title={formMode === 'add' ? 'Add New FAQ' : 'Edit FAQ'}
        subtitle={formMode === 'add' ? 'Create a new frequently asked question' : `Update FAQ #${selectedFaq?.ID}`}
        size="lg"
        height="fixed"
      >
        <FaqAddEdit
          faq={selectedFaq}
          onSave={handleSaveFaq}
          onCancel={handleFormCancel}
          mode={formMode}
        />
      </Modal>

      {/* View Modal */}
      <FaqView
        faq={selectedFaq}
        isOpen={isViewOpen}
        onClose={handleViewClose}
        onEdit={handleViewEdit}
        onViewRelated={handleViewRelated}
      />
    </div>
  );
}
