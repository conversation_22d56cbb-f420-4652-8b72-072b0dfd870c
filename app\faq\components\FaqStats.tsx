'use client';

import React from 'react';
import { FaqStats, formatDate } from '../faq.model';

interface FaqStatsProps {
  stats: FaqStats;
  loading?: boolean;
}

export default function FaqStatsComponent({ stats, loading = false }: FaqStatsProps) {
  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {[...Array(4)].map((_, index) => (
          <div key={index} className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="animate-pulse">
              <div className="flex items-center">
                <div className="w-12 h-12 bg-gray-200 rounded-xl"></div>
                <div className="ml-4 flex-1">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-6 bg-gray-200 rounded w-1/2"></div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  const statItems = [
    {
      title: 'Total FAQs',
      value: stats.totalFaqs,
      icon: 'ri-question-line',
      color: 'blue',
      bgColor: 'bg-blue-500',
      lightBgColor: 'bg-blue-100',
      textColor: 'text-blue-600'
    },
    {
      title: 'Languages',
      value: stats.totalLanguages,
      icon: 'ri-global-line',
      color: 'purple',
      bgColor: 'bg-purple-500',
      lightBgColor: 'bg-purple-100',
      textColor: 'text-purple-600'
    },
    {
      title: 'Services',
      value: stats.totalServices,
      icon: 'ri-service-line',
      color: 'green',
      bgColor: 'bg-green-500',
      lightBgColor: 'bg-green-100',
      textColor: 'text-green-600'
    },
    {
      title: 'Last Updated',
      value: formatDate(stats.lastUpdated),
      icon: 'ri-time-line',
      color: 'orange',
      bgColor: 'bg-orange-500',
      lightBgColor: 'bg-orange-100',
      textColor: 'text-orange-600',
      isDate: true
    }
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {statItems.map((item, index) => (
        <div key={index} className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
          <div className="flex items-center">
            <div className={`w-12 h-12 ${item.lightBgColor} rounded-xl flex items-center justify-center`}>
              <i className={`${item.icon} ${item.textColor} text-xl`}></i>
            </div>
            <div className="ml-4 flex-1">
              <p className="text-sm font-medium text-gray-600">{item.title}</p>
              <p className={`text-2xl font-bold ${item.textColor} ${item.isDate ? 'text-sm' : ''}`}>
                {item.isDate ? item.value : item.value.toLocaleString()}
              </p>
            </div>
          </div>
          
          {/* Progress indicator for non-date items */}
          {!item.isDate && (
            <div className="mt-4">
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className={`${item.bgColor} h-2 rounded-full transition-all duration-300`}
                  style={{ 
                    width: `${Math.min(100, (item.value / Math.max(item.value, 10)) * 100)}%` 
                  }}
                ></div>
              </div>
            </div>
          )}
        </div>
      ))}
    </div>
  );
}
