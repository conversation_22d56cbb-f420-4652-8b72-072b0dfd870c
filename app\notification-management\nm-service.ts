import axiosInstance from "../api/axiosInstance";
import {
  NotificationSetting,
  NotificationTemplate,
  ProviderConfig,
  CreateNotificationTemplateRequest,
  UpdateNotificationTemplateRequest,
  CreateProviderConfigRequest,
  UpdateProviderConfigRequest,
  CreateNotificationSettingRequest,
  UpdateNotificationSettingRequest
} from "./nm.model";

//Notfication Templates
// Get all templates
export const getNotificationTemplates = async (retries = 3): Promise<NotificationTemplate[]> => {
  try {
    const response = await axiosInstance.get<NotificationTemplate[]>('/notification-templates');
    return (response.data ?? []) as NotificationTemplate[];
  } catch (error: any) {
    console.error('Error fetching notification templates:', error);

    // Retry logic for network errors
    if (retries > 0 && (error.code === 'NETWORK_ERROR' || error.response?.status >= 500)) {
      console.log(`Retrying... ${retries} attempts left`);
      await new Promise(resolve => setTimeout(resolve, 1000));
      return getNotificationTemplates(retries - 1);
    }
    throw error;
  }
};

// Get a template by id
export const getNotificationTemplatesByID = async (id: string) => {
  try {
    const response = await axiosInstance.get<NotificationTemplate>(`/notification-templates/${id}`);
    if (response.data == null) {
      throw new Error('Template not found');
    }
    return response.data;
  } catch (error) {
    console.error('Error fetching notification templates by ID:', error);
    throw error;
  }
};

// Create a template
export const createNotificationTemplates = async (body: CreateNotificationTemplateRequest) => {
  try {
    const response = await axiosInstance.post<NotificationTemplate>('/notification-templates', body);
    return response.data;
  } catch (error) {
    console.error('Error creating notification template:', error);
    throw error;
  }
};

// Update a template
export const updateNotificationTemplates = async (id: string, body: UpdateNotificationTemplateRequest) => {
  try {
    const response = await axiosInstance.put<NotificationTemplate>(`/notification-templates/${id}`, body);
    return response.data;
  } catch (error) {
    console.error("Error updating notification template:", error);
    throw error;
  }
};

// Delete a template
export const deleteNotificationTemplates = async (id: string) => {
  try {
    const response = await axiosInstance.delete(`/notification-templates/${id}`);
    return response.data;
  } catch (error) {
    console.error("Error deleting notification template:", error);
    throw error;
  }
};



//Provider configurations
// Get all provider configs
export const getProviderConfigs = async (retries = 3): Promise<ProviderConfig[]> => {
  try {
    const response = await axiosInstance.get<ProviderConfig[]>('/provider-configs');
    return (response.data ?? []) as ProviderConfig[];
  } catch (error: any) {
    console.error('Error fetching provider configurations:', error);

    // Retry logic for network errors
    if (retries > 0 && (error.code === 'NETWORK_ERROR' || error.response?.status >= 500)) {
      console.log(`Retrying... ${retries} attempts left`);
      await new Promise(resolve => setTimeout(resolve, 1000));
      return getProviderConfigs(retries - 1);
    }

    throw error;
  }
};

// Get a provider config by id
export const getProviderConfigsByID = async (id: string) => {
  try {
    const response = await axiosInstance.get<ProviderConfig>(`/provider-configs/${id}`);
    if (response.data == null) {
      throw new Error('Provider config not found');
    }
    return response.data;
  } catch (error) {
    console.error('Error fetching provider configurations by ID:', error);
    throw error;
  }
};

// Create a provider config
export const createProviderConfigs = async (body: CreateProviderConfigRequest) => {
  try {
    const response = await axiosInstance.post<ProviderConfig>('/provider-configs', body);
    return response.data;
  } catch (error) {
    console.error('Error creating provider configurations:', error);
    throw error;
  }
};

// Update a provider config
export const updateProviderConfigs = async (id: string, body: UpdateProviderConfigRequest) => {
  try {
    const response = await axiosInstance.put<ProviderConfig>(`/provider-configs/${id}`, body);
    return response.data;
  } catch (error) {
    console.error("Error updating provider configurations:", error);
    throw error;
  }
};

// Delete a provider config
export const deleteProviderConfigs = async (id: string) => {
  try {
    const response = await axiosInstance.delete(`/provider-configs/${id}`);
    return response.data;
  } catch (error) {
    console.error("Error deleting provider configurations:", error);
    throw error;
  }
};



//Notification settings
// Get all notification settings
export const getNotificationSettings = async (retries = 3): Promise<NotificationSetting[]> => {
  try {
    const response = await axiosInstance.get<NotificationSetting[]>('/notification-setting');
    return (response.data ?? []) as NotificationSetting[];
  } catch (error: any) {
    console.error('Error fetching notification settings:', error);

    // Retry logic for network errors
    if (retries > 0 && (error.code === 'NETWORK_ERROR' || error.response?.status >= 500)) {
      console.log(`Retrying... ${retries} attempts left`);
      await new Promise(resolve => setTimeout(resolve, 1000));
      return getNotificationSettings(retries - 1);
    }

    throw error;
  }
};

// Get a notification setting by id
export const getNotificationSettingsByID = async (id: string) => {
  try {
    const response = await axiosInstance.get<NotificationSetting>(`/notification-setting/${id}`);
    if (response.data == null) {
      throw new Error('Notification setting not found');
    }
    return response.data;
  } catch (error) {
    console.error('Error fetching notification settings by ID:', error);
    throw error;
  }
};

// Create a notification setting
export const createNotificationSettings = async (body: CreateNotificationSettingRequest) => {
  try {
    const response = await axiosInstance.post<NotificationSetting>('/notification-setting', body);
    return response.data;
  } catch (error) {
    console.error('Error creating notification settings:', error);
    throw error;
  }
};

// Update a notification setting
export const updateNotificationSettings = async (id: string, body: UpdateNotificationSettingRequest) => {
  try {
    const response = await axiosInstance.put<NotificationSetting>(`/notification-setting/${id}`, body);
    return response.data;
  } catch (error) {
    console.error("Error updating notification settings:", error);
    throw error;
  }
};

// Delete a notification setting
export const deleteNotificationSettings = async (id: string) => {
  try {
    const response = await axiosInstance.delete(`/notification-setting/${id}`);
    return response.data;
  } catch (error) {
    console.error("Error deleting notification settings:", error);
    throw error;
  }
};
