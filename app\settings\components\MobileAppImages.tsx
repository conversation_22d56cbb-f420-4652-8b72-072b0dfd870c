// 'use client';

// import React from 'react';

// interface MobileAppImage {
//   id: string;
//   url: string;
//   alt: string;
//   enabled: boolean;
// }

// interface Props {
//   images: MobileAppImage[];
//   onToggle: (id: string) => void;
//   onUpload: () => void;
//   onDelete: (id: string) => void;
// }

// const MobileAppImages: React.FC<Props> = ({ images, onToggle, onUpload, onDelete }) => {
//   return (
//     <div className="animate-fade-in bg-white rounded-lg shadow-sm border border-gray-200">
//       <div className="p-6 border-b border-gray-200 flex items-center justify-between">
//         <div className="flex items-center space-x-3">
//           <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
//             <i className="ri-image-line text-green-600 text-lg"></i>
//           </div>
//           <div>
//             <h2 className="text-lg font-semibold text-gray-900">Mobile App Images</h2>
//             <p className="text-sm text-gray-600">Manage promotional images for mobile app</p>
//           </div>
//         </div>
//         <button
//           onClick={onUpload}
//           className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg font-medium hover:bg-green-700 transition-colors"
//         >
//           <i className="ri-upload-2-line mr-2"></i>
//           Upload Image
//         </button>
//       </div>

//       <div className="p-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
//         {images.map((image) => (
//           <div key={image.id} className="bg-gray-50 rounded-lg border border-gray-200 overflow-hidden">
//             <div className="aspect-video bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center">
//               <i className="ri-image-line text-gray-400 text-4xl"></i>
//             </div>
//             <div className="p-4">
//               <div className="flex items-center justify-between mb-3">
//                 <span className="text-sm font-medium text-gray-900">{image.alt}</span>
//                 <button
//                   onClick={() => onToggle(image.id)}
//                   className={`relative inline-flex h-5 w-9 items-center rounded-full transition-colors ${
//                     image.enabled ? 'bg-green-600' : 'bg-gray-300'
//                   }`}
//                 >
//                   <span
//                     className={`inline-block h-3 w-3 transform rounded-full bg-white transition-transform ${
//                       image.enabled ? 'translate-x-5' : 'translate-x-1'
//                     }`}
//                   />
//                 </button>
//               </div>
//               <div className="flex items-center justify-between">
//                 <span
//                   className={`text-xs font-medium ${
//                     image.enabled ? 'text-green-600' : 'text-gray-500'
//                   }`}
//                 >
//                   {image.enabled ? 'Active' : 'Inactive'}
//                 </span>
//                 <div className="flex items-center space-x-2">
//                   <button className="text-blue-600 hover:text-blue-800 transition-colors" title="Edit">
//                     <i className="ri-edit-line text-lg"></i>
//                   </button>
//                   <button
//                     onClick={() => onDelete(image.id)}
//                     className="text-red-600 hover:text-red-800 transition-colors"
//                     title="Delete"
//                   >
//                     <i className="ri-delete-bin-line text-lg"></i>
//                   </button>
//                 </div>
//               </div>
//             </div>
//           </div>
//         ))}
//       </div>
//     </div>
//   );
// };

// export default MobileAppImages;

'use client';

import React, { useRef } from 'react';

interface MobileAppImage {
  id: string;
  url: string;
  alt: string;
  enabled: boolean;
}

interface Props {
  image: MobileAppImage | null;
  onToggle: () => void;
  onUpload: (file: File) => void;
  onDelete: () => void;
}

const MobileAppImages: React.FC<Props> = ({ image, onToggle, onUpload, onDelete }) => {
  const fileInputRef = useRef<HTMLInputElement | null>(null);

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) onUpload(file);
  };

  return (
    <div className="animate-fade-in bg-white rounded-lg shadow border border-gray-200">
      {/* Header */}
      <div className="p-6 border-b border-gray-200 flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
            <i className="ri-image-line text-green-600 text-xl"></i>
          </div>
          <div>
            <h2 className="text-lg font-semibold text-gray-900">Mobile App Image</h2>
            <p className="text-sm text-gray-600">Upload or manage your promotional app image</p>
          </div>
        </div>

        <button
          onClick={() => fileInputRef.current?.click()}
          className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg font-medium hover:bg-green-700 transition-colors"
        >
          <i className="ri-upload-2-line mr-2"></i>
          {image ? 'Replace Image' : 'Upload Image'}
        </button>

        <input
          ref={fileInputRef}
          type="file"
          accept="image/*"
          onChange={handleFileSelect}
          className="hidden"
        />
      </div>

      {/* Image Preview */}
      <div className="p-6">
        {image ? (
          <div className="bg-gray-50 rounded-lg border border-gray-200 overflow-hidden">
            <div className="w-full h-40 bg-gray-100 flex items-center justify-center">
              {image.url ? (
                <img
                  src={image.url}
                  alt={image.alt}
                  className="object-cover w-full h-full"
                />
              ) : (
                <i className="ri-image-line text-gray-400 text-4xl"></i>
              )}
            </div>

            {/* Image Details */}
            <div className="p-4">
              <div className="flex items-center justify-between mb-3">
                <span className="text-sm font-medium text-gray-900">{image.alt}</span>
                <button
                  onClick={onToggle}
                  className={`relative inline-flex h-5 w-10 items-center rounded-full transition-colors ${
                    image.enabled ? 'bg-green-600' : 'bg-gray-300'
                  }`}
                >
                  <span
                    className={`inline-block h-3 w-3 transform rounded-full bg-white transition-transform ${
                      image.enabled ? 'translate-x-5' : 'translate-x-1'
                    }`}
                  />
                </button>
              </div>

              <div className="flex items-center justify-between">
                <span
                  className={`text-xs font-medium ${
                    image.enabled ? 'text-green-600' : 'text-gray-500'
                  }`}
                >
                  {image.enabled ? 'Active' : 'Inactive'}
                </span>
                <button
                  onClick={onDelete}
                  className="text-red-600 hover:text-red-800 transition-colors"
                  title="Delete"
                >
                  <i className="ri-delete-bin-line text-lg"></i>
                </button>
              </div>
            </div>
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center border-2 border-dashed border-gray-300 rounded-lg py-12 bg-gray-50">
            <i className="ri-image-add-line text-4xl text-gray-400 mb-3"></i>
            <p className="text-gray-600 text-sm mb-2">No image uploaded yet</p>
            <button
              onClick={() => fileInputRef.current?.click()}
              className="px-4 py-2 text-sm bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
            >
              Upload Image
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

export default MobileAppImages;
