import apiService from '../api/api-service'; 
import { SettingsResponse, UpdateSettingsRequest } from './settings.model';

const SETTINGS_ENDPOINT = '/settings';

class SettingsService {
  // GET Settings
  async getSettings(): Promise<SettingsResponse> {
    return apiService.getsettings<SettingsResponse>(SETTINGS_ENDPOINT); 
    // Make sure `getsettings` is correctly defined in your apiService
  }

  // UPDATE Settings
  async updateSettings(data: UpdateSettingsRequest): Promise<SettingsResponse> {
    const formData = new FormData();

    if (data.app_store_url !== undefined) formData.append('app_store_url', data.app_store_url);
    if (data.enable_app_store !== undefined) formData.append('enable_app_store', String(data.enable_app_store));
    if (data.play_store_url !== undefined) formData.append('play_store_url', data.play_store_url);
    if (data.enable_play_store !== undefined) formData.append('enable_play_store', String(data.enable_play_store));
    if (data.enable_markups !== undefined) formData.append('enable_markups', String(data.enable_markups));
    if (data.enable_offers !== undefined) formData.append('enable_offers', String(data.enable_offers));
    if (data.markup_calculation_basis !== undefined) formData.append('markup_calculation_basis', data.markup_calculation_basis);
    if (data.mobile_app_image) formData.append('mobile_app_image', data.mobile_app_image);

    return apiService.putFormDatasettings<SettingsResponse>(SETTINGS_ENDPOINT, formData);
    // Make sure `putFormDatasettings` is defined and uses PUT + FormData
  }
}

export const settingsService = new SettingsService();
