'use client';

import { useState, useEffect, useCallback } from 'react';
import { Coupon, CouponFilters, CouponStats, getDefaultCouponFilters } from '../coupon.model';
import CouponService from '../coupon.service';
import CouponList from './CouponList';
import CouponAddEdit from './CouponAddEdit';
import CouponView from './CouponView';
import CouponFiltersComponent from './CouponFilters';
import Modal from '../../components/ui/Modal';
import PageSectionHeader from '../../components/ui/PageSectionHeader';

export default function CouponMaster() {
  // State management
  const [coupons, setCoupons] = useState<Coupon[]>([]);
  const [filteredCoupons, setFilteredCoupons] = useState<Coupon[]>([]);
  const [stats, setStats] = useState<CouponStats>({
    totalCoupons: 0,
    activeCoupons: 0,
    expiredCoupons: 0,
    draftCoupons: 0,
    lastUpdated: new Date().toISOString()
  });
  const [filters, setFilters] = useState<CouponFilters>(getDefaultCouponFilters());
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [totalItems, setTotalItems] = useState(0);

  // Modal states
  const [isAddEditOpen, setIsAddEditOpen] = useState(false);
  const [isViewOpen, setIsViewOpen] = useState(false);
  const [selectedCoupon, setSelectedCoupon] = useState<Coupon | null>(null);
  const [editMode, setEditMode] = useState<'add' | 'edit'>('add');

  // Fetch coupons with filters - API returns simple array, implement client-side pagination
  const fetchCoupons = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const allCoupons = await CouponService.getAllCoupons(filters);
      setCoupons(allCoupons);

      // Calculate client-side pagination
      const totalItems = allCoupons.length;
      const totalPages = Math.ceil(totalItems / pageSize);

      // Apply pagination to filtered coupons
      const startIndex = (currentPage - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      const paginatedCoupons = allCoupons.slice(startIndex, endIndex);

      setFilteredCoupons(paginatedCoupons);
      setTotalPages(totalPages);
      setTotalItems(totalItems);

      // Reset to page 1 if current page is beyond available pages
      if (currentPage > totalPages && totalPages > 0) {
        setCurrentPage(1);
      }
    } catch (err) {
      console.error('Error fetching coupons:', err);
      setError('Failed to load coupons. Please try again.');
      setCoupons([]);
      setFilteredCoupons([]);
    } finally {
      setLoading(false);
    }
  }, [filters, currentPage, pageSize]);

  // Calculate stats from current coupons
  const calculateStats = useCallback(() => {
    const now = new Date();
    const activeCoupons = coupons.filter(coupon => {
      if (coupon.deleted_at) return false;
      const validFrom = coupon.valid_from ? new Date(coupon.valid_from) : null;
      const validTo = coupon.valid_to ? new Date(coupon.valid_to) : null;

      if (validFrom && validFrom > now) return false;
      if (validTo && validTo < now) return false;
      return true;
    }).length;

    const expiredCoupons = coupons.filter(coupon => {
      if (coupon.deleted_at) return false;
      const validTo = coupon.valid_to ? new Date(coupon.valid_to) : null;
      return validTo && validTo < now;
    }).length;

    const draftCoupons = coupons.filter(coupon => {
      if (coupon.deleted_at) return false;
      const validFrom = coupon.valid_from ? new Date(coupon.valid_from) : null;
      return validFrom && validFrom > now;
    }).length;

    const totalCoupons = coupons.filter(coupon => !coupon.deleted_at).length;

    setStats({
      totalCoupons,
      activeCoupons,
      expiredCoupons,
      draftCoupons,
      lastUpdated: new Date().toISOString()
    });
  }, [coupons]);

  useEffect(() => {
    fetchCoupons();
  }, [fetchCoupons]);

  useEffect(() => {
    calculateStats();
  }, [calculateStats]);

  // Handle pagination changes when coupons data is already loaded
  useEffect(() => {
    if (coupons.length > 0) {
      const startIndex = (currentPage - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      const paginatedCoupons = coupons.slice(startIndex, endIndex);
      setFilteredCoupons(paginatedCoupons);
    }
  }, [currentPage, pageSize, coupons]);

  // Event handlers
  const handleCreateCoupon = () => {
    setSelectedCoupon(null);
    setEditMode('add');
    setIsAddEditOpen(true);
  };

  const handleEditCoupon = (coupon: Coupon) => {
    setSelectedCoupon(coupon);
    setEditMode('edit');
    setIsAddEditOpen(true);
  };

  const handleViewCoupon = (coupon: Coupon) => {
    setSelectedCoupon(coupon);
    setIsViewOpen(true);
  };

  const handleDeleteCoupon = async (coupon: Coupon) => {
    if (window.confirm(`Are you sure you want to delete the coupon "${coupon.name}"?`)) {
      try {
        await CouponService.deleteCoupon(coupon.id);
        await fetchCoupons();
      } catch (error) {
        console.error('Error deleting coupon:', error);
        alert('Failed to delete coupon. Please try again.');
      }
    }
  };

  const handleSaveCoupon = async () => {
    setIsAddEditOpen(false);
    setSelectedCoupon(null);
    await fetchCoupons();
    await fetchStats();
  };

  const handleFiltersChange = (newFilters: CouponFilters) => {
    setFilters(newFilters);
    setCurrentPage(1); // Reset to first page when filters change
  };

  const handleClearFilters = () => {
    setFilters(getDefaultCouponFilters());
    setCurrentPage(1);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handlePageSizeChange = (newPageSize: number) => {
    setPageSize(newPageSize);
    setCurrentPage(1);
  };

  const handleViewToEdit = (coupon: Coupon) => {
    setIsViewOpen(false);
    setSelectedCoupon(coupon);
    setEditMode('edit');
    setIsAddEditOpen(true);
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <PageSectionHeader
        title="Coupon Management"
        subtitle="Create and manage discount coupons for your travel services"
        totalItems={totalItems}
        showAddButton={true}
        addButtonText="Add Coupon"
        onAddButtonClick={handleCreateCoupon}
      />

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                <i className="ri-coupon-line text-blue-600"></i>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Total Coupons</p>
              <p className="text-2xl font-semibold text-gray-900">{stats.totalCoupons}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                <i className="ri-check-line text-green-600"></i>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Active Coupons</p>
              <p className="text-2xl font-semibold text-gray-900">{stats.activeCoupons}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center">
                <i className="ri-time-line text-red-600"></i>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Expired Coupons</p>
              <p className="text-2xl font-semibold text-gray-900">{stats.expiredCoupons}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
                <i className="ri-draft-line text-yellow-600"></i>
              </div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Draft Coupons</p>
              <p className="text-2xl font-semibold text-gray-900">{stats.draftCoupons}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <CouponFiltersComponent
        filters={filters}
        onFiltersChange={handleFiltersChange}
        onClearFilters={handleClearFilters}
      />

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex">
            <i className="ri-error-warning-line text-red-400 mr-2"></i>
            <p className="text-sm text-red-700">{error}</p>
          </div>
        </div>
      )}

      {/* Coupon List */}
      <CouponList
        coupons={filteredCoupons}
        onEdit={handleEditCoupon}
        onView={handleViewCoupon}
        onDelete={handleDeleteCoupon}
        loading={loading}
        currentPage={currentPage}
        totalPages={totalPages}
        pageSize={pageSize}
        totalItems={totalItems}
        onPageChange={handlePageChange}
        onPageSizeChange={handlePageSizeChange}
      />

      {/* Add/Edit Modal */}
      <Modal
        isOpen={isAddEditOpen}
        onClose={() => setIsAddEditOpen(false)}
        title={editMode === 'edit' ? 'Edit Coupon' : 'Create New Coupon'}
        subtitle={editMode === 'edit' && selectedCoupon ? `Update ${selectedCoupon.name} details` : 'Add a new discount coupon'}
        size="xl"
      >
        <CouponAddEdit
          coupon={selectedCoupon}
          onSave={handleSaveCoupon}
          onCancel={() => setIsAddEditOpen(false)}
          mode={editMode}
        />
      </Modal>

      {/* View Modal */}
      <Modal
        isOpen={isViewOpen}
        onClose={() => setIsViewOpen(false)}
        title="Coupon Details"
        subtitle={selectedCoupon ? `${selectedCoupon.name} (${selectedCoupon.coupon_code})` : ''}
        size="xl"
      >
        <CouponView
          coupon={selectedCoupon}
          onEdit={handleViewToEdit}
          onClose={() => setIsViewOpen(false)}
        />
      </Modal>
    </div>
  );
}
