// services/virtual.service.ts

import apiService from '../api/api-service';
import {
  CreateVirtualGroupRequest,
  UpdateVirtualGroupRequest,
  VirtualGroup,
} from './virtual.model';

const API_URL = '/v1/virtual-groups';

class VirtualService {
  /**
   * GET /v1/virtual-groups
   * Fetches all virtual groups (only top-level by default)
   * Supports client-side pagination
   */
  async getVirtuals(page: number = 1, limit: number = 10): Promise<{ data: VirtualGroup[], total: number, page: number, totalPages: number }> {
    try {
      const response = await apiService.getvirtual<VirtualGroup[]>(API_URL);
      const topLevelGroups = response.filter(group => !group.has_parent);
      
      // Client-side pagination
      const total = topLevelGroups.length;
      const totalPages = Math.ceil(total / limit);
      const startIndex = (page - 1) * limit;
      const endIndex = startIndex + limit;
      const paginatedData = topLevelGroups.slice(startIndex, endIndex);
      
      return {
        data: paginatedData,
        total,
        page,
        totalPages
      };
    } catch (error) {
      console.error('Failed to fetch virtual groups:', error);
      throw error;
    }
  }

  /**
   * GET /v1/virtual-groups/{id}
   * Fetches a single virtual group and its nested children.
   */
  async getVirtualById(id: number): Promise<VirtualGroup> {
    try {
      const response = await apiService.getvirtual<VirtualGroup>(`${API_URL}/${id}`);
      return response;
    } catch (error) {
      console.error(`Failed to fetch virtual group ${id}:`, error);
      throw error;
    }
  }

  /**
   * POST /v1/virtual-groups
   * Creates a new nested virtual group structure.
   * Supports recursive child creation.
   */
  async createVirtual(data: CreateVirtualGroupRequest): Promise<VirtualGroup> {
    try {
      const response = await apiService.postvirtual<VirtualGroup>(API_URL, data);
      console.log('Created VirtualGroup Response:', response);
      return response;
    } catch (error) {
      console.error('Failed to create virtual group:', error);
      throw error;
    }
  }

  /**
   * PUT /v1/virtual-groups/{id}
   * Updates an existing virtual group by ID.
   */
  async updateVirtual(id: number, data: UpdateVirtualGroupRequest): Promise<VirtualGroup> {
    try {
      const response = await apiService.putvirtual<VirtualGroup>(`${API_URL}/${id}`, data);
      return response;
    } catch (error) {
      console.error(`Failed to update virtual group ${id}:`, error);
      throw error;
    }
  }

  /**
   * DELETE /v1/virtual-groups/{id}
   * Deletes a virtual group and its children.
   */
  async deleteVirtual(id: number): Promise<void> {
    try {
      await apiService.deletevirtual<void>(`${API_URL}/${id}`);
    } catch (error) {
      console.error(`Failed to delete virtual group ${id}:`, error);
      throw error;
    }
  }
}

const virtualService = new VirtualService();
export default virtualService;
