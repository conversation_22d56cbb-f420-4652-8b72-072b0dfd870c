"use client";

import { useCallback, useEffect, useState } from "react";
import NotificationSettingsList from "./components/NotificationSettingsList";
import NotificationSettingForm from "./components/NotificationSettingForm";
import Modal from "@/app/components/ui/Modal";
import TabbedModal from "@/app/components/ui/TabbedModal";
import { NotificationSetting } from "../../nm.model";
import { getNotificationSettings, getNotificationSettingsByID, createNotificationSettings, updateNotificationSettings, deleteNotificationSettings } from "../../nm-service";

export default function NotificationSettings() {
  const [settings, setSettings] = useState<NotificationSetting[]>([]);
  const [loading, setLoading] = useState(false);
  const [viewLoading, setViewLoading] = useState(false);
  const [formLoading, setFormLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const [isFormOpen, setIsFormOpen] = useState(false);
  const [selectedSetting, setSelectedSetting] = useState<NotificationSetting | null>(null);
  const [isViewMode, setIsViewMode] = useState(false);

  const fetchSettings = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const res = await getNotificationSettings();
      setSettings(res ?? []);
    } catch (err) {
      console.error("Error fetching notification settings:", err);
      setError("Failed to load notification settings");
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchSettings();
  }, [fetchSettings]);

  // Event handlers
  const handleCreateSetting = () => {
    setSelectedSetting(null);
    setIsViewMode(false);
    setIsFormOpen(true);
  };

  const handleEditSetting = (setting: NotificationSetting) => {
    setSelectedSetting(setting);
    setIsViewMode(false);
    setIsFormOpen(true);
  };

  const handleViewSetting = async (setting: NotificationSetting) => {
    try {
      setIsViewMode(true);
      setIsFormOpen(true);
      setSelectedSetting(null); // Clear previous data
      setViewLoading(true);

      // Fetch fresh data for viewing
      const freshSetting = await getNotificationSettingsByID(setting.id);
      setSelectedSetting(freshSetting);
    } catch (error) {
      console.error("Error fetching setting details:", error);
      setError("Failed to load setting details");
      setIsFormOpen(false);
    } finally {
      setViewLoading(false);
    }
  };

  const handleDeleteSetting = async (setting: NotificationSetting) => {
    if (!confirm(`Are you sure you want to delete the setting for "${setting.event_key}" on ${setting.channel}?`)) {
      return;
    }

    try {
      setFormLoading(true);
      setError(null);
      await deleteNotificationSettings(setting.id);

      // Refresh the entire list after successful deletion
      await fetchSettings();
    } catch (error: any) {
      console.error("Error deleting setting:", error);
      setError("Failed to delete setting");
    } finally {
      setFormLoading(false);
    }
  };

  const handleFormClose = () => {
    setIsFormOpen(false);
    setSelectedSetting(null);
    setIsViewMode(false);
    setError(null);
  };

  // Helper function to convert form data to JSON matching exact Go struct
  const createRequestBody = (settingData: any) => {
    // Return the data as a proper JSON object matching your Go struct payload
    return {
      company_id: parseInt(settingData.company_id) || 101, // required
      event_key: settingData.event_key, // required
      channel: settingData.channel, // required
      is_enabled: Boolean(settingData.is_enabled), // bool (default false if omitted)
      template_id: settingData.template_id // required
      // Note: id, created_at, updated_at are server-generated and not included in payload
    };
  };

  const handleFormSave = async (settingData: any) => {
    try {
      setFormLoading(true);
      setError(null);

      if (selectedSetting) {
        // Update existing setting
        const requestBody = createRequestBody(settingData);
        await updateNotificationSettings(selectedSetting.id, requestBody);

        // Refresh the entire list after successful update
        await fetchSettings();
      } else {
        // Create new setting
        const requestBody = createRequestBody(settingData);
        await createNotificationSettings(requestBody);

        // Refresh the entire list after successful creation
        await fetchSettings();
      }

      handleFormClose();
    } catch (error: any) {
      console.error("Error saving setting:", error);
      setError(selectedSetting ? "Failed to update setting" : "Failed to create setting");
    } finally {
      setFormLoading(false);
    }
  };

  const getEnabledCount = () => {
    return settings.filter(setting => setting.is_enabled).length;
  };

  const getDisabledCount = () => {
    return settings.filter(setting => !setting.is_enabled).length;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 sm:p-6">
        <div className="flex flex-col space-y-4 lg:flex-row lg:items-center lg:justify-between lg:space-y-0">
          <div className="flex-1 min-w-0">
            <h2 className="text-lg sm:text-xl font-semibold text-gray-900">
              Notification Settings
            </h2>
            <p className="text-sm text-gray-600 mt-1">
              Manage notification settings for events and channels
            </p>
          </div>
          <div className="flex flex-col space-y-2 sm:flex-row sm:items-center sm:space-y-0 sm:space-x-4">
            <div className="bg-blue-50 px-4 py-2 rounded-lg">
              <span className="text-sm font-medium text-blue-700">
                Total Settings: {settings.length}
              </span>
            </div>
            <div className="bg-green-50 px-4 py-2 rounded-lg">
              <span className="text-sm font-medium text-green-700">
                Enabled: {getEnabledCount()}
              </span>
            </div>
            <div className="bg-red-50 px-4 py-2 rounded-lg">
              <span className="text-sm font-medium text-red-700">
                Disabled: {getDisabledCount()}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Responsive Actions Bar */}
      <div className="flex flex-col space-y-4 lg:flex-row lg:items-center lg:justify-between lg:space-y-0">
        <div className="flex flex-col space-y-2 sm:flex-row sm:items-center sm:space-y-0 sm:space-x-4">
          <button
            onClick={handleCreateSetting}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 sm:py-3 rounded-lg font-medium shadow-sm transition-colors whitespace-nowrap"
          >
            <i className="ri-add-line mr-2"></i>
            Add Setting
          </button>
        </div>

        {/* Responsive Filters */}
        <div className="flex flex-col space-y-2 sm:flex-row sm:items-center sm:space-y-0 sm:space-x-4">
          <div className="relative">
            <i className="ri-search-line absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
            <input
              type="text"
              placeholder="Search Settings..."
              className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm w-full sm:w-auto min-w-48"
            />
          </div>

          <select className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm w-full sm:w-auto pr-8">
            <option value="all">All Status</option>
            <option value="enabled">Enabled</option>
            <option value="disabled">Disabled</option>
          </select>

          <select className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm w-full sm:w-auto pr-8">
            <option value="">All Channels</option>
            <option value="email">Email</option>
            <option value="sms">SMS</option>
            <option value="whatsapp">WhatsApp</option>
            <option value="fcm">FCM</option>
          </select>
        </div>
      </div>

      {/* Notification Settings List */}
      {loading ? (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-12 text-center">
          <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <i className="ri-loader-line text-2xl text-blue-600 animate-spin"></i>
          </div>
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Loading Settings</h3>
          <p className="text-gray-600">Fetching notification settings...</p>
        </div>
      ) : (
        <NotificationSettingsList
          settings={settings}
          onEdit={handleEditSetting}
          onView={handleViewSetting}
          onDelete={handleDeleteSetting}
          onCreate={handleCreateSetting}
          loading={formLoading}
        />
      )}

      {/* Notification Setting View Modal */}
      {isFormOpen && isViewMode && (
        <NotificationSettingViewTabbedModal
          setting={selectedSetting}
          isOpen={isFormOpen && isViewMode}
          onClose={handleFormClose}
          loading={viewLoading}
        />
      )}

      {/* Notification Setting Form Modal */}
      {isFormOpen && !isViewMode && (
        <Modal
          isOpen={isFormOpen && !isViewMode}
          onClose={handleFormClose}
          title={selectedSetting ? 'Edit Notification Setting' : 'Create Notification Setting'}
          subtitle={selectedSetting ? `Editing setting: ${selectedSetting.event_key} • ${selectedSetting.channel}` : 'Create a new notification setting'}
          size="xl"
          height="fixed"
          footer={
            <div className="flex items-center justify-between">
              <div className="flex items-center text-sm text-gray-500">
                {formLoading ? (
                  <>
                    <i className="ri-loader-line mr-1 animate-spin"></i>
                    {selectedSetting ? "Updating..." : "Creating..."}
                  </>
                ) : (
                  <>
                    <i className="ri-save-line mr-1"></i>
                    Auto-saved
                  </>
                )}
              </div>
              <div className="flex items-center space-x-3">
                <button
                  type="button"
                  onClick={handleFormClose}
                  disabled={formLoading}
                  className="px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  form="notification-setting-form"
                  disabled={formLoading}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                >
                  {formLoading && <i className="ri-loader-line mr-2 animate-spin"></i>}
                  {selectedSetting ? 'Update Setting' : 'Create Setting'}
                </button>
              </div>
            </div>
          }
        >
          {error && (
            <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
              <div className="flex items-center">
                <i className="ri-error-warning-line text-red-600 mr-2"></i>
                <span className="text-red-700 text-sm">{error}</span>
                <button
                  onClick={() => setError(null)}
                  className="ml-auto text-red-600 hover:text-red-800"
                >
                  <i className="ri-close-line"></i>
                </button>
              </div>
            </div>
          )}
          <NotificationSettingForm
            setting={selectedSetting}
            onSave={handleFormSave}
            onCancel={handleFormClose}
            loading={formLoading}
          />
        </Modal>
      )}
    </div>
  );
}

// Notification Setting View Modal Component
function NotificationSettingViewTabbedModal({
  setting,
  isOpen,
  onClose,
  loading
}: {
  setting: NotificationSetting | null;
  isOpen: boolean;
  onClose: () => void;
  loading: boolean;
}) {
  // Show loading state
  if (loading || !setting) {
    return (
      <TabbedModal
        isOpen={isOpen}
        onClose={onClose}
        title="Loading Setting..."
        subtitle="Please wait while we fetch the setting details"
        tabs={[
          {
            id: 'loading',
            label: 'Loading',
            content: (
              <div className="flex items-center justify-center py-12">
                <div className="text-center">
                  <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i className="ri-loader-line text-2xl text-blue-600 animate-spin"></i>
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">Loading Setting Details</h3>
                  <p className="text-gray-600">Please wait while we fetch the latest information...</p>
                </div>
              </div>
            )
          }
        ]}
        defaultTab="loading"
        size="full"
        height="fixed"
      />
    );
  }
  const getStatusBadge = (isEnabled?: boolean) => {
    return (
      <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${
        isEnabled
          ? 'bg-green-100 text-green-800 border border-green-200'
          : 'bg-red-100 text-red-800 border border-red-200'
      }`}>
        <div className={`w-2 h-2 rounded-full mr-2 ${
          isEnabled ? 'bg-green-500' : 'bg-red-500'
        }`}></div>
        {isEnabled ? 'Enabled' : 'Disabled'}
      </span>
    );
  };

  const getChannelIcon = (channel: string) => {
    switch (channel.toLowerCase()) {
      case 'email':
        return 'ri-mail-line';
      case 'sms':
        return 'ri-message-2-line';
      case 'push':
        return 'ri-notification-line';
      default:
        return 'ri-send-plane-line';
    }
  };

  const getChannelColor = (channel: string) => {
    switch (channel.toLowerCase()) {
      case 'email':
        return 'text-blue-600 bg-blue-100';
      case 'sms':
        return 'text-green-600 bg-green-100';
      case 'push':
        return 'text-purple-600 bg-purple-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getEventKeyLabel = (eventKey: string) => {
    const eventLabels: Record<string, string> = {
      user_signup: 'User Signup',
      password_reset: 'Password Reset',
      booking_confirmation: 'Booking Confirmation',
      booking_cancellation: 'Booking Cancellation',
      payment_success: 'Payment Success',
      payment_failed: 'Payment Failed',
      welcome_email: 'Welcome Email',
      account_verification: 'Account Verification',
      booking_reminder: 'Booking Reminder',
      review_request: 'Review Request'
    };
    return eventLabels[eventKey] || eventKey.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  const tabs = [
    {
      id: 'overview',
      label: 'Overview',
      icon: 'ri-information-line',
      content: (
        <div className="p-6 space-y-6">
          {/* Setting Header */}
          <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl p-6 border border-blue-100">
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center space-x-4">
                <div className={`w-16 h-16 rounded-2xl flex items-center justify-center ${getChannelColor(setting.channel)}`}>
                  <i className={`${getChannelIcon(setting.channel)} text-2xl`}></i>
                </div>
                <div>
                  <h3 className="text-xl font-bold text-gray-900">{getEventKeyLabel(setting.event_key)}</h3>
                  <p className="text-gray-600 mt-1">{setting.channel.toUpperCase()} • Template: {setting.template_id}</p>
                </div>
              </div>
              {getStatusBadge(setting.is_enabled)}
            </div>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="bg-white rounded-lg p-4 border border-blue-200">
                <div className="text-sm text-gray-600">Event Key</div>
                <div className="font-semibold text-gray-900">{setting.event_key}</div>
              </div>
              <div className="bg-white rounded-lg p-4 border border-blue-200">
                <div className="text-sm text-gray-600">Channel</div>
                <div className="font-semibold text-gray-900 capitalize">{setting.channel}</div>
              </div>
              <div className="bg-white rounded-lg p-4 border border-blue-200">
                <div className="text-sm text-gray-600">Template ID</div>
                <div className="font-semibold text-gray-900 font-mono text-xs">{setting.template_id}</div>
              </div>
              <div className="bg-white rounded-lg p-4 border border-blue-200">
                <div className="text-sm text-gray-600">Company ID</div>
                <div className="font-semibold text-gray-900">{setting.company_id}</div>
              </div>
            </div>
          </div>

          {/* Setting Details */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
              <div className="flex items-center mb-4">
                <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                  <i className="ri-time-line text-green-600 text-lg"></i>
                </div>
                <h3 className="text-lg font-semibold text-gray-900">Timeline</h3>
              </div>
              <div className="space-y-4">
                <div className="flex justify-between items-center py-2 border-b border-gray-100">
                  <span className="text-sm text-gray-600">Created</span>
                  <span className="text-sm font-medium text-gray-900">{formatDate(setting.created_at)}</span>
                </div>
                <div className="flex justify-between items-center py-2">
                  <span className="text-sm text-gray-600">Last Updated</span>
                  <span className="text-sm font-medium text-gray-900">{formatDate(setting.updated_at)}</span>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
              <div className="flex items-center mb-4">
                <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                  <i className="ri-settings-3-line text-purple-600 text-lg"></i>
                </div>
                <h3 className="text-lg font-semibold text-gray-900">Configuration</h3>
              </div>
              <div className="space-y-4">
                <div className="flex justify-between items-center py-2 border-b border-gray-100">
                  <span className="text-sm text-gray-600">Status</span>
                  {getStatusBadge(setting.is_enabled)}
                </div>
                <div className="flex justify-between items-center py-2 border-b border-gray-100">
                  <span className="text-sm text-gray-600">Event Type</span>
                  <span className="text-sm font-medium text-gray-900">
                    {getEventKeyLabel(setting.event_key)}
                  </span>
                </div>
                <div className="flex justify-between items-center py-2">
                  <span className="text-sm text-gray-600">Notification Channel</span>
                  <div className={`inline-flex items-center px-2 py-1 rounded-lg text-xs font-medium ${getChannelColor(setting.channel)}`}>
                    <i className={`${getChannelIcon(setting.channel)} mr-1`}></i>
                    {setting.channel.toUpperCase()}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )
    },
    {
      id: 'configuration',
      label: 'Configuration',
      icon: 'ri-settings-line',
      content: (
        <div className="p-6 space-y-6">
          {/* Event Configuration */}
          <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
            <div className="flex items-center mb-4">
              <div className="w-10 h-10 bg-indigo-100 rounded-lg flex items-center justify-center mr-3">
                <i className="ri-flashlight-line text-indigo-600 text-lg"></i>
              </div>
              <h3 className="text-lg font-semibold text-gray-900">Event Configuration</h3>
            </div>
            <div className="bg-indigo-50 rounded-lg p-4 border border-indigo-200">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center">
                  <i className="ri-lightning-line text-indigo-600 text-xl"></i>
                </div>
                <div>
                  <label className="text-sm font-medium text-indigo-700">Event Key</label>
                  <p className="text-lg font-semibold text-indigo-900">{setting.event_key}</p>
                  <p className="text-sm text-indigo-600">
                    Triggers when "{getEventKeyLabel(setting.event_key)}" event occurs
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Channel Configuration */}
          <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
            <div className="flex items-center mb-4">
              <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                <i className="ri-send-plane-line text-green-600 text-lg"></i>
              </div>
              <h3 className="text-lg font-semibold text-gray-900">Channel Configuration</h3>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div className="flex justify-between items-center py-3 border-b border-gray-100">
                  <span className="text-sm font-medium text-gray-600">Channel Type</span>
                  <div className={`inline-flex items-center px-3 py-2 rounded-lg text-sm font-medium ${getChannelColor(setting.channel)}`}>
                    <i className={`${getChannelIcon(setting.channel)} mr-2`}></i>
                    {setting.channel.toUpperCase()}
                  </div>
                </div>
                <div className="flex justify-between items-center py-3 border-b border-gray-100">
                  <span className="text-sm font-medium text-gray-600">Template ID</span>
                  <span className="text-sm text-gray-900 font-mono">{setting.template_id}</span>
                </div>
                <div className="flex justify-between items-center py-3">
                  <span className="text-sm font-medium text-gray-600">Company ID</span>
                  <span className="text-sm text-gray-900">{setting.company_id}</span>
                </div>
              </div>
              <div className="space-y-4">
                <div className="flex justify-between items-center py-3 border-b border-gray-100">
                  <span className="text-sm font-medium text-gray-600">Status</span>
                  {getStatusBadge(setting.is_enabled)}
                </div>
                <div className="flex justify-between items-center py-3 border-b border-gray-100">
                  <span className="text-sm font-medium text-gray-600">Created</span>
                  <span className="text-sm text-gray-900">{formatDate(setting.created_at)}</span>
                </div>
                <div className="flex justify-between items-center py-3">
                  <span className="text-sm font-medium text-gray-600">Last Updated</span>
                  <span className="text-sm text-gray-900">{formatDate(setting.updated_at)}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Template Information */}
          <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-200">
            <div className="flex items-center mb-4">
              <div className="w-10 h-10 bg-amber-100 rounded-lg flex items-center justify-center mr-3">
                <i className="ri-file-text-line text-amber-600 text-lg"></i>
              </div>
              <h3 className="text-lg font-semibold text-gray-900">Associated Template</h3>
            </div>
            <div className="bg-amber-50 rounded-lg p-4 border border-amber-200">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-amber-700">Template ID</p>
                  <p className="text-lg font-semibold text-amber-900 font-mono">{setting.template_id}</p>
                  <p className="text-sm text-amber-600 mt-1">
                    This template will be used when the {getEventKeyLabel(setting.event_key)} event is triggered via {setting.channel}
                  </p>
                </div>
                <button className="inline-flex items-center px-3 py-2 bg-amber-600 text-white rounded-lg font-medium hover:bg-amber-700 transition-colors">
                  <i className="ri-eye-line mr-2"></i>
                  View Template
                </button>
              </div>
            </div>
          </div>
        </div>
      )
    }
  ];

  return (
    <TabbedModal
      isOpen={isOpen}
      onClose={onClose}
      title={`${getEventKeyLabel(setting.event_key)}`}
      subtitle={`${setting.event_key} • ${setting.channel.toUpperCase()} • ${setting.template_id}`}
      tabs={tabs}
      defaultTab="overview"
      size="full"
      height="fixed"
      headerActions={
        <div className="flex items-center space-x-2">
          {getStatusBadge(setting.is_enabled)}
          <div className={`inline-flex items-center px-3 py-2 rounded-lg text-sm font-medium ${getChannelColor(setting.channel)}`}>
            <i className={`${getChannelIcon(setting.channel)} mr-2`}></i>
            {setting.channel.toUpperCase()}
          </div>
        </div>
      }
    />
  );
}
