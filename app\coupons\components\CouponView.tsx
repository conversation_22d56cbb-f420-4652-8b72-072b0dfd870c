'use client';

import { Coupon, CouponUtils } from '../coupon.model';

interface CouponViewProps {
  coupon: Coupon | null;
  onEdit?: (coupon: Coupon) => void;
  onClose: () => void;
}

export default function CouponView({ coupon, onEdit, onClose }: CouponViewProps) {
  if (!coupon) {
    return (
      <div className="p-6 text-center">
        <p className="text-gray-500">No coupon data available</p>
      </div>
    );
  }

  const status = CouponUtils.getCouponStatus(coupon);
  const isValid = CouponUtils.isValidNow(coupon);

  const getStatusConfig = () => {
    const configs = {
      ACTIVE: { bg: 'bg-green-100', text: 'text-green-800', icon: 'ri-check-line' },
      INACTIVE: { bg: 'bg-gray-100', text: 'text-gray-800', icon: 'ri-pause-line' },
      EXPIRED: { bg: 'bg-red-100', text: 'text-red-800', icon: 'ri-time-line' },
      DRAFT: { bg: 'bg-yellow-100', text: 'text-yellow-800', icon: 'ri-draft-line' }
    };
    return configs[status];
  };

  const statusConfig = getStatusConfig();

  return (
    <div className="space-y-6">
      {/* Coupon Header */}
      <div className="flex items-center space-x-4 p-6 bg-gradient-to-r from-purple-50 to-blue-50 rounded-lg border border-purple-200">
        <div className="w-16 h-16 bg-gradient-to-br from-purple-100 to-purple-200 rounded-xl flex items-center justify-center">
          {coupon.image ? (
            <img 
              src={coupon.image} 
              alt={coupon.name}
              className="w-16 h-16 rounded-xl object-cover"
            />
          ) : (
            <i className="ri-coupon-line text-purple-700 text-2xl"></i>
          )}
        </div>
        <div className="flex-1">
          <h2 className="text-2xl font-bold text-gray-900">{coupon.name}</h2>
          <p className="text-lg text-gray-600 font-mono">{coupon.coupon_code}</p>
          <div className="flex items-center space-x-4 mt-2">
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusConfig.bg} ${statusConfig.text}`}>
              <i className={`${statusConfig.icon} mr-1`}></i>
              {status}
            </span>
            {isValid && (
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                <i className="ri-check-double-line mr-1"></i>
                Currently Valid
              </span>
            )}
          </div>
        </div>
        {onEdit && (
          <button
            onClick={() => onEdit(coupon)}
            className="px-4 py-2 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 text-sm font-medium text-gray-700"
          >
            <i className="ri-edit-line mr-2"></i>
            Edit
          </button>
        )}
      </div>

      {/* Coupon Details */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Basic Information */}
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Basic Information</h3>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-500">Coupon Type</label>
              <p className="text-lg font-semibold text-gray-900">{CouponUtils.getTypeLabel(coupon.coupon_type)}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-500">Discount Value</label>
              <p className="text-2xl font-bold text-purple-600">
                {CouponUtils.formatCouponValue(coupon.coupon_value, coupon.coupon_type)}
              </p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-500">Business Type</label>
              <p className="text-lg font-semibold text-gray-900">{CouponUtils.getBusinessTypeLabel(coupon.business_type)}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-500">Service Type</label>
              <p className="text-lg font-semibold text-gray-900">{CouponUtils.getServiceTypeLabel(coupon.service_type)}</p>
            </div>
          </div>
        </div>

        {/* Validity Information */}
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Validity Information</h3>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-500">Valid From</label>
              <p className="text-lg font-semibold text-gray-900">
                {coupon.valid_from ? CouponUtils.formatDate(coupon.valid_from) : 'No start date'}
              </p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-500">Valid To</label>
              <p className="text-lg font-semibold text-gray-900">
                {CouponUtils.formatDate(coupon.valid_to)}
              </p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-500">Current Status</label>
              <div className="flex items-center space-x-2">
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusConfig.bg} ${statusConfig.text}`}>
                  <i className={`${statusConfig.icon} mr-1`}></i>
                  {status}
                </span>
                {isValid ? (
                  <span className="text-sm text-green-600">✓ Active and usable</span>
                ) : (
                  <span className="text-sm text-red-600">✗ Not currently usable</span>
                )}
              </div>
            </div>
            {coupon.user_id && (
              <div>
                <label className="block text-sm font-medium text-gray-500">User Specific</label>
                <p className="text-lg font-semibold text-gray-900">User ID: {coupon.user_id}</p>
                <p className="text-sm text-blue-600">This coupon is restricted to a specific user</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Description and Terms */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Description & Terms</h3>
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-500 mb-2">Description</label>
            <p className="text-gray-900 leading-relaxed">{coupon.description}</p>
          </div>
          {coupon.terms_and_conditions && (
            <div>
              <label className="block text-sm font-medium text-gray-500 mb-2">Terms and Conditions</label>
              <div className="bg-gray-50 rounded-lg p-4">
                <p className="text-gray-700 leading-relaxed whitespace-pre-wrap">{coupon.terms_and_conditions}</p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Metadata */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Metadata</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-3">
            <div>
              <label className="block text-sm font-medium text-gray-500">Created</label>
              <p className="text-sm text-gray-900">{CouponUtils.formatDate(coupon.created_at)}</p>
              <p className="text-xs text-gray-500">by {coupon.created_by}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-500">Last Updated</label>
              <p className="text-sm text-gray-900">{CouponUtils.formatDate(coupon.updated_at)}</p>
              <p className="text-xs text-gray-500">by {coupon.updated_by}</p>
            </div>
          </div>
          <div className="space-y-3">
            <div>
              <label className="block text-sm font-medium text-gray-500">Coupon ID</label>
              <p className="text-sm text-gray-900 font-mono">#{coupon.id}</p>
            </div>
            {coupon.deleted_at && (
              <div>
                <label className="block text-sm font-medium text-gray-500">Deleted</label>
                <p className="text-sm text-red-600">{CouponUtils.formatDate(coupon.deleted_at)}</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Usage Information (if available) */}
      {(coupon.flights || coupon.hotels) && (
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Usage Restrictions</h3>
          <div className="space-y-4">
            {coupon.flights && (
              <div>
                <label className="block text-sm font-medium text-gray-500 mb-2">Flight Restrictions</label>
                <div className="bg-blue-50 rounded-lg p-3">
                  <pre className="text-sm text-blue-800 whitespace-pre-wrap">{JSON.stringify(coupon.flights, null, 2)}</pre>
                </div>
              </div>
            )}
            {coupon.hotels && (
              <div>
                <label className="block text-sm font-medium text-gray-500 mb-2">Hotel Restrictions</label>
                <div className="bg-green-50 rounded-lg p-3">
                  <pre className="text-sm text-green-800 whitespace-pre-wrap">{JSON.stringify(coupon.hotels, null, 2)}</pre>
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex items-center justify-end space-x-4 pt-6 border-t border-gray-200">
        <button
          onClick={onClose}
          className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          Close
        </button>
        {onEdit && (
          <button
            onClick={() => onEdit(coupon)}
            className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <i className="ri-edit-line mr-2"></i>
            Edit Coupon
          </button>
        )}
      </div>
    </div>
  );
}
